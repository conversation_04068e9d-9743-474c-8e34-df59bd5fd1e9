<?php
/**
 * 初始化管理员账号
 */
// 加载必要文件
require_once dirname(__FILE__) . '/../config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';

// 创建管理员表
$db = DB::getInstance();

// 检查表是否存在
$tableExists = $db->query("SHOW TABLES LIKE '{$db->getPrefix()}admins'")->rowCount() > 0;

if (!$tableExists) {
    // 创建管理员表
    $sql = "CREATE TABLE `{$db->getPrefix()}admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL,
        `password` varchar(255) NOT NULL,
        `email` varchar(100) DEFAULT NULL,
        `last_login_time` datetime DEFAULT NULL,
        `last_login_ip` varchar(50) DEFAULT NULL,
        `create_time` datetime NOT NULL,
        `update_time` datetime NOT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    $db->query($sql);
    
    // 创建默认管理员账号
    $admin = [
        'username' => 'admin',
        'password' => password_hash('123456', PASSWORD_DEFAULT),
        'email' => '<EMAIL>',
        'create_time' => date('Y-m-d H:i:s'),
        'update_time' => date('Y-m-d H:i:s')
    ];
    
    $db->insert('admins', $admin);
    
    echo '管理员表创建成功，默认账号：admin，密码：123456';
} else {
    // 检查是否有管理员账号
    $adminCount = count($db->getAll('admins'));
    
    if ($adminCount === 0) {
        // 创建默认管理员账号
        $admin = [
            'username' => 'admin',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'email' => '<EMAIL>',
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s')
        ];
        
        $db->insert('admins', $admin);
        
        echo '默认管理员账号已创建，账号：admin，密码：123456';
    } else {
        echo '管理员表已存在，且已有管理员账号';
    }
}