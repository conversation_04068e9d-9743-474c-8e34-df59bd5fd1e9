<?php
/**
 * 管理后台公共头部
 */
session_start();

// 检查是否登录
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 加载必要文件
require_once '../config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';

// 加载管理后台函数库
require_once 'common/functions/data.php';
require_once 'common/functions/security.php';
require_once 'common/functions/ui.php';

// 获取当前页面名称
$currentPage = basename($_SERVER['SCRIPT_NAME']);

// 导航菜单
$navMenus = [
    'index.php' => [
        'name' => '控制台',
        'icon' => 'layui-icon-home'
    ],
    'domains.php' => [
        'name' => '域名管理',
        'icon' => 'layui-icon-website'
    ],
    'programs.php' => [
        'name' => '程序管理',
        'icon' => 'layui-icon-app'
    ],
    'downloads.php' => [
        'name' => '下载记录',
        'icon' => 'layui-icon-download-circle'
    ],
    'logs.php' => [
        'name' => '操作日志',
        'icon' => 'layui-icon-log'
    ],
    'settings.php' => [
        'name' => '系统设置',
        'icon' => 'layui-icon-set'
    ]
];
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>管理后台 - <?php echo SITE_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../assets/layui/css/layui.css">
    <script src="../assets/layui/layui.js"></script>
    <script>
        // 配置Layui模块
        layui.config({
            base: '../admin/assets/js/', // 自定义模块目录
            version: '<?php echo date("YmdHi"); ?>' // 更新缓存
        }).extend({
            admin: 'admin' // 注册admin模块
        });
    </script>
    <style>
        .site-header {
            height: 60px;
            background-color: #23262E;
            color: #fff;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .site-logo {
            font-size: 20px;
            font-weight: bold;
        }
        .site-nav {
            display: flex;
            align-items: center;
        }
        .user-info {
            margin-left: 20px;
        }
        .main-container {
            display: flex;
            min-height: calc(100vh - 60px);
        }
        .side-nav {
            width: 220px;
            background-color: #393D49;
            color: #fff;
        }
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f2f2f2;
        }
        .layui-nav-item a {
            padding-left: 30px;
        }
        .content-header {
            margin-bottom: 20px;
        }
        .content-body {
            background-color: #fff;
            padding: 20px;
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="site-header">
        <div class="site-logo"><?php echo SITE_NAME; ?> - 管理后台</div>
        <div class="site-nav">
            <a href="../" target="_blank" class="layui-btn layui-btn-primary layui-btn-sm">前台首页</a>
            <div class="user-info">
                <i class="layui-icon layui-icon-username"></i>
                <?php echo $_SESSION['admin_username']; ?>
                <a href="logout.php" class="layui-btn layui-btn-danger layui-btn-sm">退出</a>
            </div>
        </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="main-container">
        <!-- 左侧导航 -->
        <div class="side-nav">
            <ul class="layui-nav layui-nav-tree" lay-filter="side-nav">
                <?php foreach ($navMenus as $url => $menu): ?>
                <li class="layui-nav-item <?php echo $currentPage === $url ? 'layui-this' : ''; ?>">
                    <a href="<?php echo $url; ?>">
                        <i class="layui-icon <?php echo $menu['icon']; ?>"></i>
                        <?php echo $menu['name']; ?>
                    </a>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
        
        <!-- 右侧内容 -->
        <div class="main-content"><?php
// 显示页面标题
if (isset($navMenus[$currentPage])) {
    echo '<div class="content-header">';
    echo '<h2>' . $navMenus[$currentPage]['name'] . '</h2>';
    echo '</div>';
}
?>
            <div class="content-body">