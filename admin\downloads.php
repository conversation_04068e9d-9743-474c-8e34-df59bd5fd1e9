<?php
/**
 * 下载记录管理页面
 */
// 包含头部
require_once 'common/header.php';

// 生成CSRF令牌
$csrfToken = generateCsrfToken();
?>

<!-- 数据表格 -->
<table id="download-table" lay-filter="download-table"></table>

<!-- 搜索表单 -->
<script type="text/html" id="search-form">
    <form class="layui-form" style="margin-bottom: 10px;">
        <div class="layui-inline">
            <div class="layui-input-inline" style="width: 150px;">
                <input type="text" name="domain" placeholder="域名" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <div class="layui-input-inline" style="width: 120px;">
                <select name="is_trial">
                    <option value="">授权类型</option>
                    <option value="0">正式版</option>
                    <option value="1">试用版</option>
                </select>
            </div>
        </div>
        <div class="layui-inline">
            <div class="layui-input-inline" style="width: 120px;">
                <input type="text" name="start_date" id="start-date" placeholder="开始日期" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <div class="layui-input-inline" style="width: 120px;">
                <input type="text" name="end_date" id="end-date" placeholder="结束日期" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <button class="layui-btn" lay-submit lay-filter="search-submit">搜索</button>
            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
        </div>
    </form>
</script>

<!-- 操作按钮模板 -->
<script type="text/html" id="table-operation">
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<script>
layui.use(['table', 'form', 'layer', 'laydate'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var laydate = layui.laydate;
    var $ = layui.jquery;
    
    // 渲染搜索表单
    $('#search-form').html($('#search-form').html());
    form.render();
    
    // 渲染日期选择器
    laydate.render({
        elem: '#start-date'
    });
    laydate.render({
        elem: '#end-date'
    });
    
    // 表格渲染
    var downloadTable = table.render({
        elem: '#download-table',
        url: 'ajax.php?action=get_downloads',
        page: true,
        toolbar: '#search-form',
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'domain', title: '域名', width: 150},
            {field: 'auth_code', title: '授权码', width: 300},
            {field: 'download_time', title: '下载时间', width: 170, sort: true},
            {field: 'ip', title: 'IP地址', width: 120},
            {field: 'program_id', title: '程序ID', width: 80},
            {field: 'is_trial', title: '授权类型', width: 100, templet: function(d){
                return d.is_trial == 1 ? '<span class="layui-badge">试用</span>' : '<span class="layui-badge layui-bg-green">正式</span>';
            }},
            {fixed: 'right', title: '操作', toolbar: '#table-operation', width: 80}
        ]],
        response: {
            statusCode: 0
        },
        parseData: function(res){
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.data.count,
                "data": res.data.list
            };
        }
    });
    
    // 监听搜索表单提交
    form.on('submit(search-submit)', function(data){
        var field = data.field;
        
        // 重载表格
        downloadTable.reload({
            where: field,
            page: {
                curr: 1
            }
        });
        
        return false;
    });
    
    // 监听工具条
    table.on('tool(download-table)', function(obj){
        var data = obj.data;
        
        if(obj.event === 'del'){
            layer.confirm('确定删除该记录吗？', function(index){
                // 发送删除请求
                $.ajax({
                    url: 'ajax.php?action=delete_download',
                    type: 'POST',
                    data: {
                        id: data.id,
                        csrf_token: '<?php echo $csrfToken; ?>'
                    },
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                
                layer.close(index);
            });
        }
    });
    
    // 批量删除按钮
    $('body').on('click', '.layui-btn-danger', function(){
        var checkStatus = table.checkStatus('download-table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要删除的记录');
            return;
        }
        
        layer.confirm('确定删除选中的' + data.length + '条记录吗？', function(index){
            var ids = [];
            for(var i = 0; i < data.length; i++){
                ids.push(data[i].id);
            }
            
            // 发送删除请求
            $.ajax({
                url: 'ajax.php?action=batch_delete_downloads',
                type: 'POST',
                data: {
                    ids: ids,
                    csrf_token: '<?php echo $csrfToken; ?>'
                },
                dataType: 'json',
                success: function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1});
                        // 刷新表格
                        downloadTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
            
            layer.close(index);
        });
    });
});
</script>

<?php
// 包含底部
require_once 'common/footer.php';
?>