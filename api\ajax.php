<?php

/**
 * API接口处理文件
 */
session_start();

// 加载必要文件
require_once '../config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';
require_once INCLUDE_PATH . 'auth.php';

// 处理跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 预检请求直接返回成功
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    header('HTTP/1.1 200 OK');
    exit;
}

// 检查操作类型
$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

// 处理不同的操作
switch ($action) {
    // 验证授权码
    case 'verify_auth':
        $authCode = isset($_POST['auth_code']) ? $_POST['auth_code'] : '';
        $domain = isset($_POST['domain']) ? cleanInput($_POST['domain']) : '';
        $clientIP = isset($_POST['client_ip']) ? $_POST['client_ip'] : '';
        $verifyToken = isset($_POST['verify_token']) ? $_POST['verify_token'] : '';
        $timestamp = isset($_POST['timestamp']) ? intval($_POST['timestamp']) : 0;

        // 验证参数
        if (empty($authCode) || empty($domain)) {
            jsonResponse(1, '参数不完整');
        }

        // 时间戳验证（防止重放攻击）
        if (abs(time() - $timestamp) > 300) { // 5分钟有效期
            jsonResponse(1, '请求已过期');
        }
        // 验证token
        $expectedToken = md5($authCode . date('Ymd', $timestamp) . $domain);
        if ($verifyToken !== $expectedToken) {
            jsonResponse(1, '验证失败');
        }

        // 验证授权码
        $db = DB::getInstance();
        $domainInfo = $db->get('domains', ['domain' => $domain]);
        if (!$domainInfo) {
            // 先尝试提取主域名进行检查
            $domainParts = explode('.', $domain);
            $partsCount = count($domainParts);

            // 只检查至少包含两部分的域名
            if ($partsCount >= 2) {
                $mainDomain = $domainParts[$partsCount - 2] . '.' . $domainParts[$partsCount - 1];
                // 特殊顶级域名处理
                if ($partsCount > 2) {
                    $specialTLDs = ['co.uk', 'com.cn', 'net.cn', 'org.cn', 'gov.cn', 'ac.cn', 'co.jp', 'co.nz', 'co.za'];
                    $lastTwo = $domainParts[$partsCount - 2] . '.' . $domainParts[$partsCount - 1];

                    if (in_array($lastTwo, $specialTLDs) && $partsCount >= 3) {
                        $mainDomain = $domainParts[$partsCount - 3] . '.' . $lastTwo;
                    }
                }

                // 检查主域名是否有授权
                if ($mainDomain != $domain) {
                    $domainInfo = $db->get('domains', ['domain' => $mainDomain]);
                }
            }

            // 如果仍然找不到授权信息
            if (!$domainInfo) {
                jsonResponse(3, '域名未授权');
            }
        }

        // 验证授权码是否匹配
        if ($domainInfo['is_trial'] == 0) {
            if ($domainInfo['auth_code'] !== $authCode) {
                jsonResponse(1, '授权码无效');
            }
        }

        // 验证状态
        if ($domainInfo['status'] == 0) {
            jsonResponse(2, '授权已被禁用');
        }

        // 验证有效期
        $expireTime = strtotime($domainInfo['expire_time']);
        $currentTime = time();

        // 如果是试用授权，需要特殊处理试用期
        if ($domainInfo['is_trial'] == 1) {
            // 检查是否有试用开始时间
            if (empty($domainInfo['trial_start_time'])) {
                jsonResponse(1, '试用数据异常，请联系管理员');
            }

            // 计算试用期结束时间
            $trialStartTime = strtotime($domainInfo['trial_start_time']);
            $trialEndTime = $trialStartTime + (TRIAL_DAYS * 24 * 60 * 60);

            // 检查试用期是否已过期
            if ($currentTime > $trialEndTime) {
                jsonResponse(1, '试用期已结束', [
                    'is_trial' => true,
                    'trial_start_time' => $domainInfo['trial_start_time'],
                    'trial_end_time' => date('Y-m-d H:i:s', $trialEndTime),
                    'trial_days' => TRIAL_DAYS
                ]);
            }

            // 试用期内，使用试用期结束时间作为有效期
            $expireTime = $trialEndTime;
        } else {
            // 正式授权，检查expire_time
            if ($expireTime < $currentTime) {
                jsonResponse(1, '授权已过期');
            }
        }

        // 记录验证日志（可选）
        // 确保auth_code不会超过数据库字段长度限制
        // 假设数据库字段为32或更小，安全起见限制为32字符
        $logAuthCode = substr($authCode, 0, 32);

        $db->insert('verifications', [
            'domain' => $domain,
            'auth_code' => $logAuthCode,
            'client_ip' => $clientIP,
            'verification_time' => date('Y-m-d H:i:s'),
            'client_domain' => $domain,
            'success' => 1
        ]);

        // 计算剩余天数
        $remainingDays = ceil(($expireTime - $currentTime) / 86400);

        // 准备返回数据
        $responseData = [
            'domain' => $domainInfo['domain'],
            'expire_time' => $domainInfo['expire_time'],
            'is_trial' => (int)$domainInfo['is_trial'],
            'remaining_days' => $remainingDays
        ];

        // 如果是试用授权，添加试用相关信息
        if ($domainInfo['is_trial'] == 1) {
            $trialStartTime = strtotime($domainInfo['trial_start_time']);
            $trialEndTime = $trialStartTime + (TRIAL_DAYS * 24 * 60 * 60);

            $responseData['trial_start_time'] = $domainInfo['trial_start_time'];
            $responseData['trial_end_time'] = date('Y-m-d H:i:s', $trialEndTime);
            $responseData['trial_days'] = TRIAL_DAYS;
            $responseData['trial_remaining_days'] = $remainingDays;
        }

        // 返回授权信息
        jsonResponse(0, '授权有效', $responseData);
        break;

    // 检查域名授权状态
    case 'check_domain':
        // 获取域名参数
        $domain = isset($_REQUEST['domain']) ? cleanInput($_REQUEST['domain']) : '';
        $checkTrial = isset($_REQUEST['check_trial']) && $_REQUEST['check_trial'];

        // 验证域名格式
        if (!isValidDomain($domain)) {
            jsonResponse(1, '域名格式不正确');
        }

        // 查询域名授权状态
        $authStatus = checkDomainAuth($domain);

        // 检查域名是否可以试用
        $canTrial = true; // 默认允许试用

        // 从数据库检查域名是否已经使用过试用版
        $db = DB::getInstance();
        $domainRecord = $db->get('domains', ['domain' => $domain]);

        if ($domainRecord) {
            // 如果已有记录且曾经是试用版，则不能再试用
            if ($domainRecord['is_trial']) {
                $canTrial = false;
            }
        }

        // 返回结果
        if ($authStatus['is_auth']) {
            jsonResponse(0, 'success', [
                'is_auth' => true,
                'expire_time' => $authStatus['expire_time'],
                'is_trial' => $authStatus['is_trial'],
                'remaining_days' => $authStatus['remaining_days'],
                'can_trial' => false // 已经有授权了，不需要试用
            ]);
        } else {
            jsonResponse(1, $authStatus['msg'], [
                'is_auth' => false,
                'expire_time' => $authStatus['expire_time'],
                'is_trial' => false,
                'can_trial' => $canTrial
            ]);
        }
        break;

    // 激活试用
    case 'activate_trial':
        // 获取域名参数
        $domain = isset($_REQUEST['domain']) ? cleanInput($_REQUEST['domain']) : '';

        // 验证域名格式
        if (!isValidDomain($domain)) {
            jsonResponse(1, '域名格式不正确，请检查域名是否正确（不支持本地域名）');
        }

        // 激活试用
        $result = activateTrial($domain);

        // 返回结果
        jsonResponse($result['code'], $result['msg'], $result['data']);
        break;

    // 程序下载处理
    case 'download':
        // 获取参数
        $domain = isset($_REQUEST['domain']) ? cleanInput($_REQUEST['domain']) : '';
        $programId = isset($_REQUEST['program_id']) ? intval($_REQUEST['program_id']) : 1;

        // 验证域名格式
        if (!isValidDomain($domain)) {
            jsonResponse(1, '域名格式不正确');
        }

        // 处理下载
        $result = processDownload($domain, $programId);

        // 返回结果
        jsonResponse($result['code'], $result['msg'], $result['data']);
        break;

    // 默认操作
    default:
        jsonResponse(1, '未知操作');
}
