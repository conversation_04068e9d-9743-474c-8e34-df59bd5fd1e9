<?php
/**
 * 授权过期处理测试脚本
 * 用于验证授权过期时的正确处理逻辑
 */

// 引入授权验证文件
require_once __DIR__ . '/includes/auth_verify.php';

echo "<h2>授权过期处理测试</h2>\n";

// 模拟API返回授权过期的响应
function simulateExpiredAuthResponse($isTrialExpired = false) {
    return [
        'code' => 1,
        'msg' => $isTrialExpired ? '试用期已结束' : '授权已过期',
        'data' => [
            'is_trial' => $isTrialExpired
        ]
    ];
}

// 模拟API返回授权无效的响应
function simulateInvalidAuthResponse() {
    return [
        'code' => 2,
        'msg' => '授权码无效',
        'data' => []
    ];
}

// 测试1: 正式授权过期
echo "<h3>测试1: 正式授权过期处理</h3>\n";
$expiredResponse = simulateExpiredAuthResponse(false);
echo "模拟API响应: <pre>" . json_encode($expiredResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

// 模拟verifyAuthCodeViaAPI函数的返回值
if ($expiredResponse['code'] === 1) {
    $authResult = [
        'error_type' => 'expired',
        'message' => $expiredResponse['msg'],
        'is_trial' => isset($expiredResponse['data']['is_trial']) ? (bool)$expiredResponse['data']['is_trial'] : false
    ];
    echo "处理结果: 识别为授权过期，应显示续费提示<br>\n";
    echo "错误类型: " . $authResult['error_type'] . "<br>\n";
    echo "错误消息: " . $authResult['message'] . "<br>\n";
    echo "是否试用: " . ($authResult['is_trial'] ? '是' : '否') . "<br>\n";
} else {
    echo "处理结果: 其他错误类型<br>\n";
}

echo "<hr>\n";

// 测试2: 试用授权过期
echo "<h3>测试2: 试用授权过期处理</h3>\n";
$trialExpiredResponse = simulateExpiredAuthResponse(true);
echo "模拟API响应: <pre>" . json_encode($trialExpiredResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if ($trialExpiredResponse['code'] === 1) {
    $authResult = [
        'error_type' => 'expired',
        'message' => $trialExpiredResponse['msg'],
        'is_trial' => isset($trialExpiredResponse['data']['is_trial']) ? (bool)$trialExpiredResponse['data']['is_trial'] : false
    ];
    echo "处理结果: 识别为试用过期，应显示购买提示<br>\n";
    echo "错误类型: " . $authResult['error_type'] . "<br>\n";
    echo "错误消息: " . $authResult['message'] . "<br>\n";
    echo "是否试用: " . ($authResult['is_trial'] ? '是' : '否') . "<br>\n";
} else {
    echo "处理结果: 其他错误类型<br>\n";
}

echo "<hr>\n";

// 测试3: 授权码无效
echo "<h3>测试3: 授权码无效处理</h3>\n";
$invalidResponse = simulateInvalidAuthResponse();
echo "模拟API响应: <pre>" . json_encode($invalidResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";

if ($invalidResponse['code'] === 1) {
    echo "处理结果: 识别为授权过期<br>\n";
} else {
    echo "处理结果: 识别为授权码无效，应显示试用选项<br>\n";
    echo "错误代码: " . $invalidResponse['code'] . "<br>\n";
    echo "错误消息: " . $invalidResponse['msg'] . "<br>\n";
}

echo "<hr>\n";

// 测试4: 修复前后的对比
echo "<h3>测试4: 修复前后对比</h3>\n";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>\n";
echo "<tr><th>场景</th><th>API响应</th><th>修复前行为</th><th>修复后行为</th></tr>\n";

echo "<tr>\n";
echo "<td>正式授权过期</td>\n";
echo "<td>code=1, msg='授权已过期'</td>\n";
echo "<td>❌ 显示试用选项</td>\n";
echo "<td>✅ 显示续费提示</td>\n";
echo "</tr>\n";

echo "<tr>\n";
echo "<td>试用授权过期</td>\n";
echo "<td>code=1, msg='试用期已结束', is_trial=true</td>\n";
echo "<td>❌ 显示试用选项</td>\n";
echo "<td>✅ 显示购买提示</td>\n";
echo "</tr>\n";

echo "<tr>\n";
echo "<td>授权码无效</td>\n";
echo "<td>code=2, msg='授权码无效'</td>\n";
echo "<td>✅ 显示试用选项</td>\n";
echo "<td>✅ 显示试用选项</td>\n";
echo "</tr>\n";

echo "</table>\n";

echo "<hr>\n";

// 测试5: 实际UI展示
echo "<h3>测试5: 实际UI展示</h3>\n";
echo "<p>点击下方链接查看不同场景的实际UI效果：</p>\n";
echo "<ul>\n";
echo "<li><a href='ui_demo.php?demo=renewal' target='_blank'>正式授权过期 - 续费提示界面</a></li>\n";
echo "<li><a href='ui_demo.php?demo=error' target='_blank'>试用过期 - 购买提示界面</a></li>\n";
echo "<li><a href='ui_demo.php?demo=trial' target='_blank'>授权码无效 - 试用选项界面</a></li>\n";
echo "</ul>\n";

echo "<hr>\n";

echo "<h3>✅ 修复总结</h3>\n";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb;'>\n";
echo "<h4>主要改进：</h4>\n";
echo "<ol>\n";
echo "<li><strong>精确错误识别</strong>：通过API返回的code值区分不同错误类型</li>\n";
echo "<li><strong>正确UI显示</strong>：授权过期时显示续费提示，而不是试用选项</li>\n";
echo "<li><strong>试用区分</strong>：区分正式授权过期和试用授权过期</li>\n";
echo "<li><strong>用户体验</strong>：提供准确的错误信息和相应的解决方案</li>\n";
echo "</ol>\n";
echo "<h4>技术实现：</h4>\n";
echo "<ul>\n";
echo "<li>修改 <code>verifyAuthCodeViaAPI</code> 函数返回详细错误信息</li>\n";
echo "<li>在 <code>checkAuth</code> 函数中添加错误类型判断</li>\n";
echo "<li>根据错误类型调用相应的UI显示函数</li>\n";
echo "</ul>\n";
echo "</div>\n";
?>
