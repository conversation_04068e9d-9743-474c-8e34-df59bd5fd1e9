<?php
/**
 * 下载处理文件
 */
session_start();

// 加载必要文件
require_once 'config.php';
require_once INCLUDE_PATH . 'functions.php';

// 获取token参数
$token = isset($_GET['token']) ? $_GET['token'] : '';

// 验证token
if (empty($token) || !isset($_SESSION['download_token']) || $_SESSION['download_token'] !== $token) {
    die('无效的下载链接');
}

// 检查下载链接是否过期
if (!isset($_SESSION['download_expire']) || $_SESSION['download_expire'] < time()) {
    die('下载链接已过期');
}

// 获取文件信息
$filePath = $_SESSION['download_file'];
$fileName = $_SESSION['download_name'];

// 检查文件是否存在
if (!file_exists($filePath)) {
    die('文件不存在');
}

// 设置下载头信息
header('Content-Description: File Transfer');
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $fileName . '"');
header('Expires: 0');
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Content-Length: ' . filesize($filePath));

// 输出文件内容
readfile($filePath);

// 清除session信息
unset($_SESSION['download_token']);
unset($_SESSION['download_file']);
unset($_SESSION['download_name']);
unset($_SESSION['download_expire']);

// 设置定时清理临时文件的任务
// 注意：在实际环境中，应该使用cron job或其他方式定期清理临时文件
// 这里简单地记录删除时间
$cleanupTime = time() + 86400; // 24小时后删除
$cleanupFile = TEMP_PATH . 'cleanup.txt';
file_put_contents($cleanupFile, $filePath . '|' . $cleanupTime . PHP_EOL, FILE_APPEND);