# 域名授权系统下载流程设计

创建时间: [2024-08-19 11:10:00 +08:00]
创建者: 系统架构师(AR)
最后更新: [2024-08-19 11:10:00 +08:00]

## 程序下载流程

### 整体流程图

```
+---------------+    +-----------------+    +-------------------+
| 用户提交域名  | -> | 验证域名授权状态 | -> | 授权有效？        |
+---------------+    +-----------------+    +---+---------------+
                                             |  |
                                             |  | Yes
                                             |  v
                     +----------------+    +-+----------------+
                     | 返回错误信息   | <- | No               |
                     +----------------+    | 选择程序进行下载 |
                                           +---+--------------+
                                               |
                                               v
+------------------------+    +-------------------+    +----------------------+
| 生成授权码并记录到数据库 | <- | 复制程序到临时目录 | <- | 获取程序源文件      |
+------------+-----------+    +-------------------+    +----------------------+
             |
             v
+---------------------------+    +------------------+
| 修改includes/authcode.php | -> | 打包程序为ZIP文件 |
+---------------------------+    +--------+---------+
                                          |
                                          v
     +-------------------+    +-------------------------+    +-------------------+
     | 清理临时文件      | <- | 记录下载信息到数据库    | <- | 提供下载链接      |
     +-------------------+    +-------------------------+    +-------------------+
```

### 关键步骤详解

#### 1. 验证域名授权状态

```php
/**
 * 验证域名授权状态
 * @param string $domain 要验证的域名
 * @return array 验证结果，包含状态和相关信息
 */
function verifyDomainAuth($domain) {
    global $db;
    
    // 净化域名输入
    $domain = filter_var($domain, FILTER_SANITIZE_STRING);
    
    // 查询数据库
    $stmt = $db->prepare("SELECT * FROM domains WHERE domain = ? AND status = 1 LIMIT 1");
    $stmt->bind_param("s", $domain);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return [
            'status' => false,
            'message' => '域名未授权'
        ];
    }
    
    $domainData = $result->fetch_assoc();
    
    // 检查是否过期
    if (!empty($domainData['expire_time'])) {
        $expireTime = strtotime($domainData['expire_time']);
        if (time() > $expireTime) {
            return [
                'status' => false,
                'message' => '域名授权已过期',
                'expire_time' => $domainData['expire_time']
            ];
        }
    }
    
    return [
        'status' => true,
        'message' => '域名已授权',
        'domain_data' => $domainData
    ];
}
```

#### 2. 程序源文件管理

```php
/**
 * 获取可用程序列表
 * @return array 程序列表
 */
function getAvailablePrograms() {
    global $db;
    
    $stmt = $db->prepare("SELECT * FROM programs WHERE 1 ORDER BY name ASC");
    $stmt->execute();
    $result = $stmt->get_result();
    
    $programs = [];
    while ($row = $result->fetch_assoc()) {
        $programs[] = $row;
    }
    
    return $programs;
}

/**
 * 获取程序详情
 * @param int $programId 程序ID
 * @return array|null 程序详情或null
 */
function getProgramById($programId) {
    global $db;
    
    $stmt = $db->prepare("SELECT * FROM programs WHERE id = ? LIMIT 1");
    $stmt->bind_param("i", $programId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}
```

#### 3. 文件处理和授权码嵌入

```php
/**
 * 处理程序下载
 * @param string $domain 域名
 * @param int $programId 程序ID
 * @return array 处理结果
 */
function processProgramDownload($domain, $programId) {
    global $db;
    
    // 1. 验证域名授权
    $authResult = verifyDomainAuth($domain);
    if (!$authResult['status']) {
        return [
            'status' => false,
            'message' => $authResult['message']
        ];
    }
    
    // 2. 获取程序信息
    $program = getProgramById($programId);
    if (!$program) {
        return [
            'status' => false,
            'message' => '程序不存在'
        ];
    }
    
    // 3. 创建临时目录
    $tempDir = 'temp/' . uniqid('download_', true);
    if (!mkdir($tempDir, 0755, true)) {
        return [
            'status' => false,
            'message' => '创建临时目录失败'
        ];
    }
    
    // 4. 复制程序文件到临时目录
    $programPath = $program['path'];
    if (!copyDirectory($programPath, $tempDir)) {
        removeDirectory($tempDir);
        return [
            'status' => false,
            'message' => '复制程序文件失败'
        ];
    }
    
    // 5. 生成授权码
    $expireTime = $authResult['domain_data']['expire_time'] ?? date('Y-m-d H:i:s', strtotime('+1 year'));
    $authCode = generateAuthCode($domain, $expireTime);
    
    // 6. 更新数据库中的授权码
    $stmt = $db->prepare("UPDATE domains SET auth_code = ?, update_time = NOW() WHERE domain = ?");
    $stmt->bind_param("ss", $authCode, $domain);
    $stmt->execute();
    
    // 7. 写入授权码到程序文件
    if (!writeAuthCodeToProgram($tempDir, $authCode)) {
        removeDirectory($tempDir);
        return [
            'status' => false,
            'message' => '写入授权码失败'
        ];
    }
    
    // 8. 打包程序为ZIP文件
    $zipFileName = 'temp/' . sanitizeFileName($program['name']) . '_' . date('Ymd') . '.zip';
    if (!createZipArchive($tempDir, $zipFileName)) {
        removeDirectory($tempDir);
        return [
            'status' => false,
            'message' => '创建ZIP文件失败'
        ];
    }
    
    // 9. 记录下载信息
    $ip = $_SERVER['REMOTE_ADDR'];
    $stmt = $db->prepare("INSERT INTO downloads (domain, auth_code, download_time, ip, program_id) VALUES (?, ?, NOW(), ?, ?)");
    $stmt->bind_param("sssi", $domain, $authCode, $ip, $programId);
    $stmt->execute();
    $downloadId = $db->insert_id;
    
    // 10. 生成下载令牌
    $downloadToken = generateDownloadToken($downloadId, $domain);
    
    // 11. 安排清理临时目录的任务
    // 实际实现可能使用cron作业或其他方式
    // scheduleTempDirCleanup($tempDir);
    
    return [
        'status' => true,
        'message' => '处理成功',
        'download_token' => $downloadToken,
        'download_url' => 'download.php?token=' . $downloadToken
    ];
}
```

#### 4. 下载链接和安全控制

```php
/**
 * 生成下载令牌
 * @param int $downloadId 下载记录ID
 * @param string $domain 域名
 * @return string 下载令牌
 */
function generateDownloadToken($downloadId, $domain) {
    $secret = 'YOUR_SECRET_KEY';
    $expiry = time() + 3600; // 1小时有效期
    
    $data = [
        'id' => $downloadId,
        'domain' => $domain,
        'exp' => $expiry
    ];
    
    $base = json_encode($data);
    $signature = hash_hmac('sha256', $base, $secret);
    
    return base64_encode($base) . '.' . $signature;
}

/**
 * 验证下载令牌
 * @param string $token 下载令牌
 * @return array 验证结果
 */
function verifyDownloadToken($token) {
    $secret = 'YOUR_SECRET_KEY';
    
    $parts = explode('.', $token);
    if (count($parts) !== 2) {
        return ['valid' => false, 'message' => '无效的令牌格式'];
    }
    
    list($base, $signature) = $parts;
    
    $expectedSignature = hash_hmac('sha256', $base, $secret);
    if ($signature !== $expectedSignature) {
        return ['valid' => false, 'message' => '令牌签名无效'];
    }
    
    $data = json_decode(base64_decode($base), true);
    
    if (!$data || !isset($data['exp'])) {
        return ['valid' => false, 'message' => '令牌数据无效'];
    }
    
    if (time() > $data['exp']) {
        return ['valid' => false, 'message' => '令牌已过期'];
    }
    
    return [
        'valid' => true,
        'data' => $data
    ];
}
```

## 定时任务

为了确保临时文件得到清理，系统需要设置定时任务。可以通过两种方式实现：

### 1. Cron 任务

在服务器上设置cron任务，定期运行清理脚本：

```bash
# 每天凌晨2点运行清理脚本
0 2 * * * php /path/to/auth/cron/cleanup.php
```

清理脚本 (`cleanup.php`) 示例：

```php
<?php
// 清理24小时前的临时文件
$tempDir = __DIR__ . '/../temp';
$cutoffTime = time() - 86400; // 24小时

$iterator = new DirectoryIterator($tempDir);
foreach ($iterator as $fileInfo) {
    if ($fileInfo->isDot() || $fileInfo->isDir()) {
        continue;
    }
    
    if ($fileInfo->getMTime() < $cutoffTime) {
        unlink($fileInfo->getPathname());
    }
}

// 清理临时目录
$iterator = new DirectoryIterator($tempDir);
foreach ($iterator as $fileInfo) {
    if ($fileInfo->isDot() || !$fileInfo->isDir()) {
        continue;
    }
    
    if ($fileInfo->getMTime() < $cutoffTime) {
        removeDirectory($fileInfo->getPathname());
    }
}

/**
 * 递归删除目录
 * @param string $dir 目录路径
 * @return bool 是否成功
 */
function removeDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $objects = scandir($dir);
    foreach ($objects as $object) {
        if ($object == "." || $object == "..") {
            continue;
        }
        
        $path = $dir . '/' . $object;
        if (is_dir($path)) {
            removeDirectory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}
?>
```

### 2. 按需清理

在每次下载处理完成后，将清理任务添加到队列：

```php
/**
 * 安排临时目录清理
 * @param string $tempDir 临时目录路径
 */
function scheduleTempDirCleanup($tempDir) {
    global $db;
    
    $cleanupTime = date('Y-m-d H:i:s', strtotime('+24 hours'));
    $stmt = $db->prepare("INSERT INTO cleanup_tasks (path, cleanup_time, created_at) VALUES (?, ?, NOW())");
    $stmt->bind_param("ss", $tempDir, $cleanupTime);
    $stmt->execute();
}
```

然后在每次页面请求时检查是否有需要清理的任务：

```php
/**
 * 执行待清理任务
 */
function executeCleanupTasks() {
    global $db;
    
    $stmt = $db->prepare("SELECT * FROM cleanup_tasks WHERE cleanup_time <= NOW() LIMIT 10");
    $stmt->execute();
    $result = $stmt->get_result();
    
    while ($task = $result->fetch_assoc()) {
        // 执行清理
        if (is_dir($task['path'])) {
            removeDirectory($task['path']);
        } elseif (file_exists($task['path'])) {
            unlink($task['path']);
        }
        
        // 从任务表中删除
        $stmt2 = $db->prepare("DELETE FROM cleanup_tasks WHERE id = ?");
        $stmt2->bind_param("i", $task['id']);
        $stmt2->execute();
    }
}
```

## 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|-----|------|---------|-------|
| v0.1 | [2024-08-19 11:10:00 +08:00] | 初始下载流程设计 | AR |