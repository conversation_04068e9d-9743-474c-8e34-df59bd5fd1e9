<?php
/**
 * 管理后台AJAX处理文件 (优化版)
 */
session_start();

// 加载必要文件
require_once '../config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';
require_once INCLUDE_PATH . 'auth.php';
require_once 'common/functions/data.php';
require_once 'common/functions/security.php';

// 检查操作类型
$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

// 除了登录操作外，所有请求都需要验证登录状态和CSRF
if (!in_array($action, ['login'])) {
    apiPreCheck(true, true);
}

// 处理不同的操作
switch ($action) {
    // 获取域名列表
    case 'get_domains':
        $data = getDataList('domains');
        jsonResponse(0, 'success', $data);
        break;
    
    // 添加域名
    case 'add_domain':
        $db = DB::getInstance();
        
        // 获取参数
        $domain = isset($_POST['domain']) ? cleanInput($_POST['domain']) : '';
        $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
        $isTrial = isset($_POST['is_trial']) ? intval($_POST['is_trial']) : 0;
        $expireTime = isset($_POST['expire_time']) ? $_POST['expire_time'] : '';
        
        // 验证域名格式
        if (!isValidDomain($domain)) {
            jsonResponse(1, '域名格式不正确');
        }
        
        // 检查域名是否已存在
        $exists = $db->get('domains', ['domain' => $domain]);
        if ($exists) {
            jsonResponse(1, '域名已存在');
        }
        
        // 生成授权码
        $authCode = generateAuthCode($domain, $expireTime, $isTrial == 1);
        
        // 当前时间
        $now = date('Y-m-d H:i:s');
        
        // 试用开始时间
        $trialStartTime = $isTrial == 1 ? $now : null;
        
        // 插入数据
        $result = $db->insert('domains', [
            'domain' => $domain,
            'auth_code' => $authCode,
            'status' => $status,
            'is_trial' => $isTrial,
            'trial_start_time' => $trialStartTime,
            'expire_time' => $expireTime,
            'create_time' => $now,
            'update_time' => $now
        ]);
        
        if ($result) {
            // 记录操作日志
            logAdminAction('add_domain', "添加域名: {$domain}", $db->lastInsertId(), 'domain');
            jsonResponse(0, '添加成功');
        } else {
            jsonResponse(1, '添加失败');
        }
        break;
    
    // 更新域名
    case 'update_domain':
        $db = DB::getInstance();
        
        // 获取参数
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $domain = isset($_POST['domain']) ? cleanInput($_POST['domain']) : '';
        $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
        $isTrial = isset($_POST['is_trial']) ? intval($_POST['is_trial']) : 0;
        $expireTime = isset($_POST['expire_time']) ? $_POST['expire_time'] : '';
        
        // 验证域名格式
        if (!isValidDomain($domain)) {
            jsonResponse(1, '域名格式不正确');
        }
        
        // 检查域名是否存在
        $domainInfo = $db->get('domains', ['id' => $id]);
        if (!$domainInfo) {
            jsonResponse(1, '域名不存在');
        }
        
        // 检查域名是否被其他记录使用
        if ($domain !== $domainInfo['domain']) {
            $exists = $db->get('domains', ['domain' => $domain]);
            if ($exists) {
                jsonResponse(1, '域名已被其他记录使用');
            }
        }
        
        // 如果需要重新生成授权码
        $authCode = $domainInfo['auth_code'];
        $regenerate = false;
        
        // 如果域名、过期时间或授权类型有变化，需要重新生成授权码
        if ($domain !== $domainInfo['domain'] || $expireTime !== $domainInfo['expire_time'] || $isTrial != $domainInfo['is_trial']) {
            $authCode = generateAuthCode($domain, $expireTime, $isTrial == 1);
            $regenerate = true;
        }
        
        // 当前时间
        $now = date('Y-m-d H:i:s');
        
        // 试用开始时间
        $trialStartTime = $isTrial == 1 ? ($domainInfo['trial_start_time'] ?: $now) : null;
        
        // 更新数据
        $result = $db->update('domains', [
            'domain' => $domain,
            'auth_code' => $authCode,
            'status' => $status,
            'is_trial' => $isTrial,
            'trial_start_time' => $trialStartTime,
            'expire_time' => $expireTime,
            'update_time' => $now
        ], ['id' => $id]);
        
        if ($result) {
            // 记录操作日志
            logAdminAction('update_domain', "更新域名: {$domain}", $id, 'domain');
            jsonResponse(0, $regenerate ? '更新成功，已重新生成授权码' : '更新成功');
        } else {
            jsonResponse(1, '更新失败');
        }
        break;
    
    // 删除域名
    case 'delete_domain':
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        // 获取域名信息用于日志记录
        $db = DB::getInstance();
        $domain = $db->get('domains', ['id' => $id]);
        
        // 删除记录
        $result = deleteRecord('domains', $id);
        
        if ($result) {
            // 记录操作日志
            logAdminAction('delete_domain', "删除域名: {$domain['domain']}", $id, 'domain');
            jsonResponse(0, '删除成功');
        } else {
            jsonResponse(1, '删除失败');
        }
        break;
    
    // 批量删除域名
    case 'batch_delete_domains':
        $ids = isset($_POST['ids']) ? $_POST['ids'] : [];
        
        if (empty($ids)) {
            jsonResponse(1, '请选择要删除的记录');
        }
        
        // 记录操作日志
        $db = DB::getInstance();
        foreach ($ids as $id) {
            $domain = $db->get('domains', ['id' => intval($id)]);
            if ($domain) {
                logAdminAction('delete_domain', "批量删除域名: {$domain['domain']}", $id, 'domain');
            }
        }
        
        // 批量删除
        $success = batchDeleteRecords('domains', $ids);
        
        jsonResponse(0, "成功删除{$success}条记录");
        break;
    
    // 更新域名状态
    case 'update_domain_status':
        $db = DB::getInstance();
        
        // 获取参数
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
        
        // 获取域名信息用于日志记录
        $domain = $db->get('domains', ['id' => $id]);
        
        // 更新数据
        $result = $db->update('domains', [
            'status' => $status,
            'update_time' => date('Y-m-d H:i:s')
        ], ['id' => $id]);
        
        if ($result) {
            // 记录操作日志
            $statusText = $status ? '启用' : '禁用';
            logAdminAction('update_domain_status', "{$statusText}域名: {$domain['domain']}", $id, 'domain');
            jsonResponse(0, '状态更新成功');
        } else {
            jsonResponse(1, '状态更新失败');
        }
        break;
    
    // 获取程序列表
    case 'get_programs':
        $data = getDataList('programs');
        jsonResponse(0, 'success', $data);
        break;
    
    // 添加程序
    case 'add_program':
        $db = DB::getInstance();
        
        // 获取参数
        $name = isset($_POST['name']) ? cleanInput($_POST['name']) : '';
        $version = isset($_POST['version']) ? cleanInput($_POST['version']) : '';
        $path = isset($_POST['path']) ? cleanInput($_POST['path']) : '';
        $description = isset($_POST['description']) ? cleanInput($_POST['description']) : '';
        
        // 验证参数
        if (empty($name) || empty($version) || empty($path)) {
            jsonResponse(1, '请填写完整信息');
        }
        
        // 检查路径是否存在
        $programPath = PROGRAM_PATH . $path;
        if (!is_dir($programPath)) {
            jsonResponse(1, '程序路径不存在');
        }
        
        // 检查授权码文件是否存在
        $authcodePath = $programPath . '/includes/authcode.php';
        if (!file_exists($authcodePath)) {
            jsonResponse(1, '程序结构不正确，缺少includes/authcode.php文件');
        }
        
        // 当前时间
        $now = date('Y-m-d H:i:s');
        
        // 插入数据
        $result = $db->insert('programs', [
            'name' => $name,
            'version' => $version,
            'path' => $path,
            'description' => $description,
            'create_time' => $now,
            'update_time' => $now
        ]);
        
        if ($result) {
            // 记录操作日志
            logAdminAction('add_program', "添加程序: {$name} v{$version}", $db->lastInsertId(), 'program');
            jsonResponse(0, '添加成功');
        } else {
            jsonResponse(1, '添加失败');
        }
        break;
    
    // 更新程序
    case 'update_program':
        $db = DB::getInstance();
        
        // 获取参数
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $name = isset($_POST['name']) ? cleanInput($_POST['name']) : '';
        $version = isset($_POST['version']) ? cleanInput($_POST['version']) : '';
        $path = isset($_POST['path']) ? cleanInput($_POST['path']) : '';
        $description = isset($_POST['description']) ? cleanInput($_POST['description']) : '';
        
        // 验证参数
        if (empty($name) || empty($version) || empty($path)) {
            jsonResponse(1, '请填写完整信息');
        }
        
        // 检查程序是否存在
        $program = $db->get('programs', ['id' => $id]);
        if (!$program) {
            jsonResponse(1, '程序不存在');
        }
        
        // 如果路径有变化，检查新路径是否存在
        if ($path !== $program['path']) {
            $programPath = PROGRAM_PATH . $path;
            if (!is_dir($programPath)) {
                jsonResponse(1, '程序路径不存在');
            }
            
            // 检查授权码文件是否存在
            $authcodePath = $programPath . '/includes/authcode.php';
            if (!file_exists($authcodePath)) {
                jsonResponse(1, '程序结构不正确，缺少includes/authcode.php文件');
            }
        }
        
        // 更新数据
        $result = $db->update('programs', [
            'name' => $name,
            'version' => $version,
            'path' => $path,
            'description' => $description,
            'update_time' => date('Y-m-d H:i:s')
        ], ['id' => $id]);
        
        if ($result) {
            // 记录操作日志
            logAdminAction('update_program', "更新程序: {$name} v{$version}", $id, 'program');
            jsonResponse(0, '更新成功');
        } else {
            jsonResponse(1, '更新失败');
        }
        break;
    
    // 删除程序
    case 'delete_program':
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        // 获取程序信息用于日志记录
        $db = DB::getInstance();
        $program = $db->get('programs', ['id' => $id]);
        
        // 删除记录
        $result = deleteRecord('programs', $id);
        
        if ($result) {
            // 记录操作日志
            logAdminAction('delete_program', "删除程序: {$program['name']} v{$program['version']}", $id, 'program');
            jsonResponse(0, '删除成功');
        } else {
            jsonResponse(1, '删除失败');
        }
        break;
    
    // 批量删除程序
    case 'batch_delete_programs':
        $ids = isset($_POST['ids']) ? $_POST['ids'] : [];
        
        if (empty($ids)) {
            jsonResponse(1, '请选择要删除的记录');
        }
        
        // 记录操作日志
        $db = DB::getInstance();
        foreach ($ids as $id) {
            $program = $db->get('programs', ['id' => intval($id)]);
            if ($program) {
                logAdminAction('delete_program', "批量删除程序: {$program['name']} v{$program['version']}", $id, 'program');
            }
        }
        
        // 批量删除
        $success = batchDeleteRecords('programs', $ids);
        
        jsonResponse(0, "成功删除{$success}条记录");
        break;
    
    // 获取下载记录
    case 'get_downloads':
        // 构建查询条件
        $where = '';
        $params = [];
        
        // 域名搜索
        if (isset($_GET['domain']) && !empty($_GET['domain'])) {
            $domain = cleanInput($_GET['domain']);
            $where .= "domain LIKE ?";
            $params[] = "%{$domain}%";
        }
        
        // 授权类型搜索
        if (isset($_GET['is_trial']) && $_GET['is_trial'] !== '') {
            $isTrial = intval($_GET['is_trial']);
            if (!empty($where)) {
                $where .= " AND ";
            }
            $where .= "is_trial = ?";
            $params[] = $isTrial;
        }
        
        // 日期范围搜索
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $startDate = $_GET['start_date'];
            if (!empty($where)) {
                $where .= " AND ";
            }
            $where .= "download_time >= ?";
            $params[] = $startDate . ' 00:00:00';
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $endDate = $_GET['end_date'];
            if (!empty($where)) {
                $where .= " AND ";
            }
            $where .= "download_time <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        $data = getDataList('downloads', $where, $params, 'download_time DESC');
        jsonResponse(0, 'success', $data);
        break;
    
    // 删除下载记录
    case 'delete_download':
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        
        // 获取下载记录信息用于日志记录
        $db = DB::getInstance();
        $download = $db->get('downloads', ['id' => $id]);
        
        // 删除记录
        $result = deleteRecord('downloads', $id);
        
        if ($result) {
            // 记录操作日志
            logAdminAction('delete_download', "删除下载记录: {$download['domain']}", $id, 'download');
            jsonResponse(0, '删除成功');
        } else {
            jsonResponse(1, '删除失败');
        }
        break;
    
    // 批量删除下载记录
    case 'batch_delete_downloads':
        $ids = isset($_POST['ids']) ? $_POST['ids'] : [];
        
        if (empty($ids)) {
            jsonResponse(1, '请选择要删除的记录');
        }
        
        // 批量删除
        $success = batchDeleteRecords('downloads', $ids);
        
        // 记录操作日志
        logAdminAction('batch_delete_downloads', "批量删除下载记录: {$success}条", 0, 'download');
        
        jsonResponse(0, "成功删除{$success}条记录");
        break;
    
    // 更新系统设置
    case 'update_settings':
        // 获取参数
        $type = isset($_POST['type']) ? $_POST['type'] : '';
        
        if ($type === 'basic') {
            // 更新基本设置
            $siteName = isset($_POST['site_name']) ? cleanInput($_POST['site_name']) : '';
            $siteDescription = isset($_POST['site_description']) ? cleanInput($_POST['site_description']) : '';
            $trialDays = isset($_POST['trial_days']) ? intval($_POST['trial_days']) : 3;
            
            // 验证参数
            if (empty($siteName) || empty($siteDescription) || $trialDays <= 0) {
                jsonResponse(1, '请填写完整信息');
            }
            
            // 读取配置文件
            $configFile = ROOT_PATH . '/config.php';
            $configContent = file_get_contents($configFile);
            
            // 替换配置项
            $configContent = preg_replace('/define\(\'SITE_NAME\', \'(.*?)\'\);/', "define('SITE_NAME', '{$siteName}');", $configContent);
            $configContent = preg_replace('/define\(\'SITE_DESCRIPTION\', \'(.*?)\'\);/', "define('SITE_DESCRIPTION', '{$siteDescription}');", $configContent);
            $configContent = preg_replace('/define\(\'TRIAL_DAYS\', (\d+)\);/', "define('TRIAL_DAYS', {$trialDays});", $configContent);
            
            // 写入配置文件
            if (file_put_contents($configFile, $configContent)) {
                // 记录操作日志
                logAdminAction('update_settings', "更新系统设置: 基本设置", 0, 'settings');
                jsonResponse(0, '设置更新成功，刷新页面后生效');
            } else {
                jsonResponse(1, '设置更新失败，无法写入配置文件');
            }
        } elseif ($type === 'password') {
            // 更新管理员密码
            $oldPassword = isset($_POST['old_password']) ? $_POST['old_password'] : '';
            $newPassword = isset($_POST['new_password']) ? $_POST['new_password'] : '';
            $confirmPassword = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
            
            // 验证参数
            if (empty($oldPassword) || empty($newPassword) || empty($confirmPassword)) {
                jsonResponse(1, '请填写完整信息');
            }
            
            if ($newPassword !== $confirmPassword) {
                jsonResponse(1, '两次输入的密码不一致');
            }
            
            if (strlen($newPassword) < 6) {
                jsonResponse(1, '密码长度不能小于6位');
            }
            
            // 验证原密码
            $db = DB::getInstance();
            $admin = $db->get('admins', ['id' => $_SESSION['admin_id']]);
            if (!$admin || !password_verify($oldPassword, $admin['password'])) {
                jsonResponse(1, '原密码不正确');
            }
            
            // 更新密码
            $result = $db->update('admins', [
                'password' => password_hash($newPassword, PASSWORD_DEFAULT),
                'update_time' => date('Y-m-d H:i:s')
            ], ['id' => $_SESSION['admin_id']]);
            
            if ($result) {
                // 记录操作日志
                logAdminAction('update_password', "更新管理员密码", $_SESSION['admin_id'], 'admin');
                jsonResponse(0, '密码修改成功');
            } else {
                jsonResponse(1, '密码修改失败');
            }
        } else {
            jsonResponse(1, '未知的设置类型');
        }
        break;
    
    // 清理临时文件
    case 'clear_temp':
        // 清理temp目录
        $tempFiles = glob(TEMP_PATH . '*');
        $count = 0;
        
        foreach ($tempFiles as $file) {
            if (is_dir($file)) {
                rmdirRecursive($file);
                $count++;
            } elseif (is_file($file) && basename($file) !== 'index.html') {
                unlink($file);
                $count++;
            }
        }
        
        // 记录操作日志
        logAdminAction('clear_temp', "清理临时文件: {$count}个", 0, 'system');
        jsonResponse(0, "成功清理{$count}个临时文件");
        break;
    
    // 数据库备份
    case 'backup_db':
        $db = DB::getInstance();
        
        // 创建备份目录
        $backupDir = ROOT_PATH . '/backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        // 备份文件名
        $backupFile = $backupDir . '/backup_' . date('YmdHis') . '.sql';
        
        // 获取所有表
        $tables = [];
        $tableResult = $db->query("SHOW TABLES LIKE '{$db->getPrefix()}%'")->fetchAll(PDO::FETCH_NUM);
        foreach ($tableResult as $table) {
            $tables[] = $table[0];
        }
        
        // 开始备份
        $sql = "-- 域名授权系统数据库备份\n";
        $sql .= "-- 备份时间: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- 服务器版本: " . $db->query("SELECT VERSION() as ver")->fetch()['ver'] . "\n\n";
        
        foreach ($tables as $table) {
            // 表结构
            $sql .= "-- 表结构: {$table}\n";
            $sql .= "DROP TABLE IF EXISTS `{$table}`;\n";
            
            $createTable = $db->query("SHOW CREATE TABLE `{$table}`")->fetch(PDO::FETCH_NUM);
            $sql .= $createTable[1] . ";\n\n";
            
            // 表数据
            $sql .= "-- 表数据: {$table}\n";
            $rows = $db->query("SELECT * FROM `{$table}`")->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($rows)) {
                $columns = array_keys($rows[0]);
                $sql .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES\n";
                
                $values = [];
                foreach ($rows as $row) {
                    $rowValues = [];
                    foreach ($row as $value) {
                        $rowValues[] = is_null($value) ? 'NULL' : "'" . addslashes($value) . "'";
                    }
                    $values[] = "(" . implode(', ', $rowValues) . ")";
                }
                $sql .= implode(",\n", $values) . ";\n\n";
            }
        }
        
        // 写入备份文件
        if (file_put_contents($backupFile, $sql)) {
            // 生成下载链接
            $downloadToken = md5($backupFile . time() . rand(1000, 9999));
            $_SESSION['backup_token'] = $downloadToken;
            $_SESSION['backup_file'] = $backupFile;
            $_SESSION['backup_name'] = basename($backupFile);
            $_SESSION['backup_expire'] = time() + 3600; // 1小时后过期
            
            // 记录操作日志
            logAdminAction('backup_db', "备份数据库: " . basename($backupFile), 0, 'system');
            jsonResponse(0, '备份成功', [
                'download_url' => 'download_backup.php?token=' . $downloadToken
            ]);
        } else {
            jsonResponse(1, '备份失败，无法写入备份文件');
        }
        break;
    
    // 获取操作日志
    case 'get_logs':
        // 构建查询条件
        $where = '';
        $params = [];
        
        // 管理员用户名搜索
        if (isset($_GET['admin_username']) && !empty($_GET['admin_username'])) {
            $adminUsername = cleanInput($_GET['admin_username']);
            $where .= "admin_username LIKE ?";
            $params[] = "%{$adminUsername}%";
        }
        
        // 操作类型搜索
        if (isset($_GET['action']) && !empty($_GET['action'])) {
            $actionType = cleanInput($_GET['action']);
            if (!empty($where)) {
                $where .= " AND ";
            }
            $where .= "action = ?";
            $params[] = $actionType;
        }
        
        // 日期范围搜索
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $startDate = $_GET['start_date'];
            if (!empty($where)) {
                $where .= " AND ";
            }
            $where .= "create_time >= ?";
            $params[] = $startDate . ' 00:00:00';
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $endDate = $_GET['end_date'];
            if (!empty($where)) {
                $where .= " AND ";
            }
            $where .= "create_time <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        $data = getDataList('admin_logs', $where, $params, 'id DESC');
        jsonResponse(0, 'success', $data);
        break;
    
    // 清空操作日志
    case 'clear_logs':
        $db = DB::getInstance();
        
        // 检查日志表是否存在
        $tableExists = $db->query("SHOW TABLES LIKE '{$db->getPrefix()}admin_logs'")->rowCount() > 0;
        
        if ($tableExists) {
            // 清空日志表
            $result = $db->query("TRUNCATE TABLE {$db->getPrefix()}admin_logs");
            
            if ($result !== false) {
                jsonResponse(0, '操作日志已清空');
            } else {
                jsonResponse(1, '清空操作日志失败');
            }
        } else {
            jsonResponse(0, '操作日志表不存在或已为空');
        }
        break;
        
    // 默认操作
    default:
        jsonResponse(1, '未知操作');
}