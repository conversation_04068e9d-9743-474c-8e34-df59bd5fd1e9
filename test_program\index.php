<?php
// 简单的授权验证测试页面

// 设置一个默认授权码（为测试用途）
$authcode = "DEFAULT_PLACEHOLDER_CODE";

// 包含授权验证文件
require_once '../programs/example_program/includes/auth_verify.php';

// 检查授权
$authValid = checkAuth();
?>
<!DOCTYPE html>
<html>
<head>
    <title>授权测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 15px; border-radius: 5px; margin: 20px 0; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>授权验证测试页面</h1>
    
    <div class="status <?php echo $authValid ? 'success' : 'error'; ?>">
        <h3>授权状态: <?php echo $authValid ? '有效' : '无效'; ?></h3>
        <p>当前域名: <?php echo htmlspecialchars($_SERVER['HTTP_HOST']); ?></p>
        <p>授权码: <?php echo htmlspecialchars($authcode); ?></p>
    </div>
    
    <h2>测试不同场景</h2>
    <form method="post">
        <div>
            <label for="authcode">设置授权码:</label>
            <input type="text" name="authcode" id="authcode" value="<?php echo htmlspecialchars($authcode); ?>">
            <button type="submit" name="action" value="set_authcode">应用</button>
        </div>
    </form>
    
    <?php
    // 处理表单提交
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'set_authcode') {
        if (isset($_POST['authcode'])) {
            echo "<script>alert('授权码已更新，页面将刷新'); window.location.reload();</script>";
            $authcode = $_POST['authcode'];
        }
    }
    ?>
</body>
</html>