# 域名授权系统架构设计 v0.1

创建时间: [2024-08-19 10:25:00 +08:00]
创建者: 系统架构师(AR)
最后更新: [2024-08-19 10:25:00 +08:00]

## 系统概述
域名授权系统是一个用于管理软件授权的Web应用，允许用户查询域名授权状态并下载授权后的程序。系统采用PHP7.4开发，使用MySQL5.7作为数据库，前端界面基于Layui框架实现。

## 架构设计
系统采用简化版MVC架构:
- **模型(Model)**: 处理数据库操作和业务逻辑
- **视图(View)**: 基于Layui实现的用户界面
- **控制器(Controller)**: 处理用户请求和路由

### 目录结构
```
/auth/
  ├── index.php              # 入口文件
  ├── config.php             # 配置文件
  ├── includes/              # 核心功能目录
  │   ├── functions.php      # 通用函数
  │   ├── db.php             # 数据库操作
  │   ├── auth.php           # 授权相关函数
  │   └── ...
  ├── admin/                 # 管理员界面
  │   ├── index.php          # 管理面板
  │   ├── domains.php        # 域名管理
  │   ├── download.php       # 下载管理
  │   └── ...
  ├── api/                   # API接口
  │   ├── index.php          # API入口
  │   └── ajax.php           # AJAX处理
  ├── download/              # 程序下载目录
  │   └── ...
  ├── assets/                # 静态资源
  │   └── layui/             # Layui框架
  └── templates/             # 前端模板
      ├── header.php
      ├── footer.php
      └── ...
```

### 数据库设计
主要表结构:

#### 域名表(domains)
```sql
CREATE TABLE `domains` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `auth_code` varchar(255) NOT NULL COMMENT '授权码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `domain` (`domain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 下载记录表(downloads)
```sql
CREATE TABLE `downloads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `auth_code` varchar(255) NOT NULL COMMENT '授权码',
  `download_time` datetime NOT NULL COMMENT '下载时间',
  `ip` varchar(50) NOT NULL COMMENT 'IP地址',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### API接口设计
系统将提供以下API接口:

1. **域名查询接口**
   - 请求方式: GET/POST
   - 接口路径: `/api/ajax.php?action=check_domain`
   - 参数: `domain` (域名)
   - 返回格式: JSON
   - 响应示例:
     ```json
     {
       "code": 0,
       "msg": "success",
       "data": {
         "is_auth": true,
         "expire_time": "2025-08-19 00:00:00"
       }
     }
     ```

2. **授权程序下载接口**
   - 请求方式: GET/POST
   - 接口路径: `/api/ajax.php?action=download`
   - 参数: `domain` (域名)
   - 返回格式: JSON/文件下载
   - 响应示例:
     ```json
     {
       "code": 0,
       "msg": "success",
       "data": {
         "download_url": "/download/program.zip"
       }
     }
     ```

### 授权机制
1. 用户提交域名进行查询
2. 系统验证域名是否已授权
3. 若已授权，允许下载程序
4. 下载时，系统自动生成授权码并写入程序的authcode.php文件
5. 授权码生成算法采用多因素混合加密方式，考虑域名、时间戳和随机盐值

## 安全考虑
1. 防止SQL注入: 所有数据库操作使用参数化查询
2. XSS防护: 输入输出过滤
3. CSRF防护: 表单Token验证
4. 授权码加密: 采用不可逆加密算法
5. 访问控制: 基于角色的权限管理

## 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|-----|------|---------|-------|
| v0.1 | [2024-08-19 10:25:00 +08:00] | 初始架构设计 | AR |