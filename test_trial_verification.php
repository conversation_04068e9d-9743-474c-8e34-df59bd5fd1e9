<?php
/**
 * 试用期验证测试脚本
 * 用于测试 verify_auth 接口的试用期判断功能
 */

require_once 'config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';

echo "<h2>试用期验证测试</h2>\n";

// 获取数据库实例
$db = DB::getInstance();

// 测试域名
$testDomain = 'trial-test.example.com';

echo "<h3>测试配置</h3>\n";
echo "试用天数: " . TRIAL_DAYS . " 天<br>\n";
echo "测试域名: " . $testDomain . "<br>\n";

// 清理测试数据
echo "<h3>清理测试数据</h3>\n";
$db->delete('domains', ['domain' => $testDomain]);
echo "已清理域名: {$testDomain}<br>\n";

// 测试1: 创建试用授权
echo "<h3>测试1: 创建试用授权</h3>\n";
$now = date('Y-m-d H:i:s');
$trialEndTime = date('Y-m-d H:i:s', strtotime("+".TRIAL_DAYS." days"));

$insertResult = $db->insert('domains', [
    'domain' => $testDomain,
    'auth_code' => 'TEST_TRIAL_CODE_123',
    'status' => 1,
    'is_trial' => 1,
    'trial_start_time' => $now,
    'expire_time' => $trialEndTime,
    'create_time' => $now,
    'update_time' => $now
]);

if ($insertResult) {
    echo "✅ 试用授权创建成功<br>\n";
    echo "试用开始时间: {$now}<br>\n";
    echo "试用结束时间: {$trialEndTime}<br>\n";
} else {
    echo "❌ 试用授权创建失败<br>\n";
}

// 测试2: 验证试用期内的授权
echo "<h3>测试2: 验证试用期内的授权</h3>\n";
$domainInfo = $db->get('domains', ['domain' => $testDomain]);

if ($domainInfo) {
    echo "域名信息:<br>\n";
    echo "- 域名: {$domainInfo['domain']}<br>\n";
    echo "- 是否试用: " . ($domainInfo['is_trial'] ? '是' : '否') . "<br>\n";
    echo "- 试用开始时间: {$domainInfo['trial_start_time']}<br>\n";
    echo "- 过期时间: {$domainInfo['expire_time']}<br>\n";
    
    // 模拟验证逻辑
    $currentTime = time();
    $trialStartTime = strtotime($domainInfo['trial_start_time']);
    $trialEndTime = $trialStartTime + (TRIAL_DAYS * 24 * 60 * 60);
    
    echo "<br>验证逻辑:<br>\n";
    echo "- 当前时间: " . date('Y-m-d H:i:s', $currentTime) . "<br>\n";
    echo "- 试用开始: " . date('Y-m-d H:i:s', $trialStartTime) . "<br>\n";
    echo "- 试用结束: " . date('Y-m-d H:i:s', $trialEndTime) . "<br>\n";
    echo "- 剩余秒数: " . ($trialEndTime - $currentTime) . "<br>\n";
    echo "- 剩余天数: " . ceil(($trialEndTime - $currentTime) / 86400) . "<br>\n";
    
    if ($currentTime > $trialEndTime) {
        echo "❌ 试用期已过期<br>\n";
    } else {
        echo "✅ 试用期有效<br>\n";
    }
} else {
    echo "❌ 未找到域名信息<br>\n";
}

// 测试3: 模拟过期的试用授权
echo "<h3>测试3: 模拟过期的试用授权</h3>\n";
$pastTime = date('Y-m-d H:i:s', strtotime('-' . (TRIAL_DAYS + 1) . ' days'));
$pastEndTime = date('Y-m-d H:i:s', strtotime('-1 days'));

$updateResult = $db->update('domains', [
    'trial_start_time' => $pastTime,
    'expire_time' => $pastEndTime,
    'update_time' => $now
], ['domain' => $testDomain]);

if ($updateResult) {
    echo "✅ 已更新为过期的试用授权<br>\n";
    echo "试用开始时间: {$pastTime}<br>\n";
    echo "试用结束时间: {$pastEndTime}<br>\n";
    
    // 重新验证
    $domainInfo = $db->get('domains', ['domain' => $testDomain]);
    $currentTime = time();
    $trialStartTime = strtotime($domainInfo['trial_start_time']);
    $trialEndTime = $trialStartTime + (TRIAL_DAYS * 24 * 60 * 60);
    
    echo "<br>验证结果:<br>\n";
    echo "- 当前时间: " . date('Y-m-d H:i:s', $currentTime) . "<br>\n";
    echo "- 试用结束: " . date('Y-m-d H:i:s', $trialEndTime) . "<br>\n";
    
    if ($currentTime > $trialEndTime) {
        echo "✅ 正确识别为试用期已过期<br>\n";
    } else {
        echo "❌ 错误：应该识别为过期<br>\n";
    }
} else {
    echo "❌ 更新失败<br>\n";
}

// 测试4: API接口测试
echo "<h3>测试4: API接口测试</h3>\n";
echo "<p>您可以使用以下方式测试API接口：</p>\n";
echo "<h4>4.1 试用期内的验证</h4>\n";

// 恢复为有效的试用授权
$db->update('domains', [
    'trial_start_time' => $now,
    'expire_time' => date("Y-m-d H:i:s", $trialEndTime),
    'update_time' => $now
], ['domain' => $testDomain]);

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<strong>POST请求到:</strong> /api/ajax.php<br>\n";
echo "<strong>参数:</strong><br>\n";
echo "- action: verify_auth<br>\n";
echo "- auth_code: TEST_TRIAL_CODE_123<br>\n";
echo "- domain: {$testDomain}<br>\n";
echo "- timestamp: " . time() . "<br>\n";
echo "- verify_token: " . md5('TEST_TRIAL_CODE_123' . date('Ymd') . $testDomain) . "<br>\n";
echo "</div>\n";

echo "<h4>4.2 试用期过期的验证</h4>\n";
// 设置为过期的试用授权
$db->update('domains', [
    'trial_start_time' => $pastTime,
    'expire_time' => $pastEndTime,
    'update_time' => $now
], ['domain' => $testDomain]);

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<strong>使用相同参数，应该返回:</strong><br>\n";
echo "- code: 1<br>\n";
echo "- msg: '试用期已结束'<br>\n";
echo "- data.is_trial: true<br>\n";
echo "- data.trial_days: " . TRIAL_DAYS . "<br>\n";
echo "</div>\n";

// 测试5: 正式授权对比
echo "<h3>测试5: 正式授权对比</h3>\n";
$formalDomain = 'formal-test.example.com';
$formalExpireTime = date('Y-m-d H:i:s', strtotime('+30 days'));
// 清理并创建正式授权
// $db->delete('domains', ['domain' => $formalDomain]);
// $db->insert('domains', [
//     'domain' => $formalDomain,
//     'auth_code' => 'FORMAL_AUTH_CODE_456',
//     'status' => 1,
//     'is_trial' => 0,
//     'trial_start_time' => null,
//     'expire_time' => $formalExpireTime,
//     'create_time' => $now,
//     'update_time' => $now
// ]);

echo "✅ 已创建正式授权对比<br>\n";
echo "正式授权域名: {$formalDomain}<br>\n";
echo "过期时间: {$formalExpireTime}<br>\n";

// 清理测试数据
echo "<h3>清理测试数据</h3>\n";
$db->delete('domains', ['domain' => $testDomain]);
$db->delete('domains', ['domain' => $formalDomain]);
echo "✅ 测试数据已清理<br>\n";

echo "<hr>\n";
echo "<h3>✅ 测试总结</h3>\n";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 1px solid #c3e6cb;'>\n";
echo "<h4>实现的功能：</h4>\n";
echo "<ol>\n";
echo "<li><strong>试用期计算</strong>：基于 trial_start_time + TRIAL_DAYS 计算试用期结束时间</li>\n";
echo "<li><strong>过期检测</strong>：准确识别试用期是否已过期</li>\n";
echo "<li><strong>数据返回</strong>：试用授权返回详细的试用信息</li>\n";
echo "<li><strong>错误区分</strong>：区分试用过期和正式授权过期</li>\n";
echo "</ol>\n";
echo "<h4>API响应格式：</h4>\n";
echo "<ul>\n";
echo "<li><strong>试用期内</strong>：code=0, 包含试用剩余天数等信息</li>\n";
echo "<li><strong>试用过期</strong>：code=1, msg='试用期已结束', data.is_trial=true</li>\n";
echo "<li><strong>正式过期</strong>：code=1, msg='授权已过期', data.is_trial=false</li>\n";
echo "</ul>\n";
echo "</div>\n";
?>
