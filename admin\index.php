<?php
/**
 * 管理后台首页
 */
// 包含头部
require_once 'common/header.php';

// 获取统计数据
$db = DB::getInstance();

// 域名总数
$domainCount = count($db->getAll('domains'));
// 试用域名数
$trialCount = count($db->getAll('domains', ['is_trial' => 1]));
// 程序总数
$programCount = count($db->getAll('programs'));
// 下载总数
$downloadCount = count($db->getAll('downloads'));

// 最近下载记录
$recentDownloads = $db->getAll('downloads', '', '*', 'download_time DESC', '5');
?>

<!-- 统计卡片 -->
<div class="layui-row layui-col-space15">
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">域名总数</div>
            <div class="layui-card-body" style="font-size: 24px;">
                <?php echo $domainCount; ?>
            </div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">试用域名</div>
            <div class="layui-card-body" style="font-size: 24px;">
                <?php echo $trialCount; ?>
            </div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">程序总数</div>
            <div class="layui-card-body" style="font-size: 24px;">
                <?php echo $programCount; ?>
            </div>
        </div>
    </div>
    <div class="layui-col-md3">
        <div class="layui-card">
            <div class="layui-card-header">下载总数</div>
            <div class="layui-card-body" style="font-size: 24px;">
                <?php echo $downloadCount; ?>
            </div>
        </div>
    </div>
</div>

<!-- 最近下载记录 -->
<div class="layui-card layui-margin-top">
    <div class="layui-card-header">最近下载记录</div>
    <div class="layui-card-body">
        <table class="layui-table">
            <thead>
                <tr>
                    <th>域名</th>
                    <th>下载时间</th>
                    <th>IP地址</th>
                    <th>程序ID</th>
                    <th>是否试用</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($recentDownloads): ?>
                    <?php foreach ($recentDownloads as $download): ?>
                    <tr>
                        <td><?php echo $download['domain']; ?></td>
                        <td><?php echo $download['download_time']; ?></td>
                        <td><?php echo $download['ip']; ?></td>
                        <td><?php echo $download['program_id']; ?></td>
                        <td><?php echo $download['is_trial'] ? '<span class="layui-badge">试用</span>' : '<span class="layui-badge layui-bg-green">正式</span>'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                <tr>
                    <td colspan="5" style="text-align: center;">暂无下载记录</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- 系统信息 -->
<div class="layui-card layui-margin-top">
    <div class="layui-card-header">系统信息</div>
    <div class="layui-card-body">
        <table class="layui-table">
            <colgroup>
                <col width="150">
                <col>
            </colgroup>
            <tbody>
                <tr>
                    <td>系统名称</td>
                    <td><?php echo SITE_NAME; ?></td>
                </tr>
                <tr>
                    <td>PHP版本</td>
                    <td><?php echo PHP_VERSION; ?></td>
                </tr>
                <tr>
                    <td>MySQL版本</td>
                    <td><?php 
                        $version = $db->query("SELECT VERSION() as ver")->fetch();
                        echo $version['ver']; 
                    ?></td>
                </tr>
                <tr>
                    <td>服务器操作系统</td>
                    <td><?php echo PHP_OS; ?></td>
                </tr>
                <tr>
                    <td>服务器时间</td>
                    <td><?php echo date('Y-m-d H:i:s'); ?></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<style>
.layui-margin-top {
    margin-top: 15px;
}
</style>

<?php
// 包含底部
require_once 'common/footer.php';
?>