<?php
/**
 * 管理后台安全相关通用函数
 */

/**
 * API请求前置安全检查
 * 
 * @param bool $needCsrf 是否需要CSRF验证
 * @param bool $needLogin 是否需要登录验证
 * @return void
 */
function apiPreCheck($needCsrf = true, $needLogin = true) {
    // 检查是否登录
    if ($needLogin && !isset($_SESSION['admin_id'])) {
        jsonResponse(401, '未登录或登录已过期');
    }
    
    // 检查CSRF令牌
    if ($needCsrf && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
        if (!validateAdminCsrfToken($token)) {
            jsonResponse(403, 'CSRF验证失败');
        }
    }
}

/**
 * 检查管理员是否登录
 * 
 * @param bool $redirect 未登录时是否重定向
 * @return bool 是否已登录
 */
function checkAdminLogin($redirect = true) {
    if (!isset($_SESSION['admin_id'])) {
        if ($redirect) {
            header('Location: login.php');
            exit;
        }
        return false;
    }
    return true;
}

/**
 * 记录管理员操作日志
 * 
 * @param string $action 操作类型
 * @param string $description 操作描述
 * @param int $targetId 操作目标ID
 * @param string $targetType 操作目标类型
 * @return bool 记录结果
 */
function logAdminAction($action, $description, $targetId = 0, $targetType = '') {
    $db = DB::getInstance();
    
    // 记录日志
    return $db->insert('admin_logs', [
        'admin_id' => $_SESSION['admin_id'],
        'admin_username' => $_SESSION['admin_username'],
        'action' => $action,
        'description' => $description,
        'target_id' => $targetId,
        'target_type' => $targetType,
        'ip' => getClientIp(),
        'create_time' => date('Y-m-d H:i:s')
    ]);
}