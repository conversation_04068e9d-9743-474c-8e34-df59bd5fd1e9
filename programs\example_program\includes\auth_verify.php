<?php

/**
 * 授权验证逻辑 - 此文件由程序开发者自行维护
 * 根据实际需求进行定制修改
 * 
 * 最后更新: 2025-06-05
 */

// ===== 配置区域 =====
define('AUTH_ROOT_URL', 'http://localhost:8099/');              // 授权系统根URL
// 授权API脚本的完整URL，基于根URL (例如: http://localhost/auth/api/ajax.php)
$AUTH_API_BASE_URL = AUTH_ROOT_URL . 'api/ajax.php';
// 授权系统Web前端地址，即根URL
define('AUTH_WEB_URL', AUTH_ROOT_URL);                        // 授权系统Web地址

// 缓存配置
define('AUTH_CACHE_DIR', __DIR__ . '/cache');                  // 缓存目录
define('AUTH_CACHE_EXPIRE', 3 * 24 * 3600);                    // 缓存有效期（3天）
define('AUTH_CACHE_ENCRYPT_KEY', 'SAs01A$sg3#!@d9981*&^FS');  // 缓存加密密钥
define('AUTH_EMERGENCY_EXPIRE', 3 * 24 * 3600);                // 紧急授权有效期（3天）

// API配置
define('AUTH_API_TIMEOUT', 10);                                // API超时时间（秒）

// 安全配置
define('AUTH_DEV_MODE_KEY', 'SECURE_DEV_KEY_HERE');            // 开发模式密钥
define('AUTH_TRIAL_DAYS', 3);                                  // 试用天数

// ===== 初始化区域 =====
// 确保缓存目录存在并受保护
initAuthSystem();

/**
 * 初始化授权系统
 */
function initAuthSystem()
{
    // 创建缓存目录
    if (!file_exists(AUTH_CACHE_DIR)) {
        @mkdir(AUTH_CACHE_DIR, 0755, true);
    }

    // 添加保护文件
    if (!file_exists(AUTH_CACHE_DIR . '/index.html')) {
        @file_put_contents(
            AUTH_CACHE_DIR . '/index.html',
            '<!DOCTYPE html><html><head><title>403 Forbidden</title></head>' .
                '<body><h1>403 Forbidden</h1><p>Access denied.</p></body></html>'
        );
    }

    // 添加.htaccess保护
    if (!file_exists(AUTH_CACHE_DIR . '/.htaccess')) {
        @file_put_contents(
            AUTH_CACHE_DIR . '/.htaccess',
            "Options -Indexes\nDeny from all"
        );
    }
}

/**
 * 检查授权状态 - 主入口函数
 * 在程序初始化时调用此函数
 * 
 * @return bool 授权有效返回true，否则返回false
 */
function checkAuth()
{
    global $authcode;

    // 1. 安全环境检查
    if (checkDebugger()) {
        showObfuscatedError("检测到非法运行环境");
        return false;
    }

    // 2. 获取当前域名
    $domain = getCurrentDomain();

    // 3. 开发模式检查
    if (isDevMode($domain)) {
        return true;
    }

    // 4. 检查授权码是否有效
    if (empty($authcode) || $authcode == "DEFAULT_PLACEHOLDER_CODE") {
        return handleEmptyAuthCode($domain);
    }

    // 5. 尝试从缓存加载授权信息
    $cacheInfo = loadAuthCache($domain, $authcode);
    if ($cacheInfo !== false) {
        return handleCachedAuth($domain, $authcode, $cacheInfo);
    }

    // 6. 通过API验证授权码
    $authInfo = verifyAuthCodeViaAPI($authcode);
    // 7.1 检查是否是授权过期错误
    if (isset($authInfo['error_type']) && $authInfo['error_type'] === 'expired') {
        // 直接显示授权过期提示
        if ($authInfo['is_trial']) {
            showAuthError("试用期已结束，请购买正式授权");
        } else {
            showAuthRenewal($authInfo['message']);
        }
        return false;
    }

    // 7.2 检查是否是授权禁用错误
    if (isset($authInfo['error_type']) && $authInfo['error_type'] === 'disabled') {
        showAuthError($authInfo['message']);
        return false;
    }

    // // 7.2.1 检查是否是授权未授权错误
    // if (isset($authInfo['error_type']) && $authInfo['error_type'] === 'unauthorized') {
    //     showAuthError($authInfo['message']);
    //     return false;
    // }

    // 7.3 处理API返回的不同情况
    if (isset($authInfo['verifyAuthCodeViaAPI']) && !$authInfo['verifyAuthCodeViaAPI']) {
        return handleInvalidAuthCode($domain);
    }


    // 8. 验证域名匹配
    if (!isDomainMatched($domain, $authInfo['domain'])) {
        showAuthError("授权码与当前域名不匹配");
        return false;
    }

    // 9. 检查授权是否过期（本地时间检查）
    if (isAuthExpired($authInfo)) {
        return handleExpiredAuth($authInfo);
    }

    // 10. 授权有效，保存到缓存
    saveAuthCache($domain, $authcode, $authInfo);

    // 11. 如果是紧急授权，显示提示
    if (isset($authInfo['is_emergency']) && $authInfo['is_emergency']) {
        showEmergencyNotice();
    }

    // 授权有效
    return true;
}

/**
 * 发送HTTP请求并获取响应
 * 
 * @param string $url 请求URL
 * @param array $postData POST数据，为空则使用GET请求
 * @param int $timeout 超时时间（秒）
 * @param int $connectTimeout 连接超时时间（秒）
 * @param bool $logErrors 是否记录错误信息
 * @return array 包含success, data, http_code, error键的关联数组
 */
function httpRequest($url, $postData = null, $timeout = 2, $connectTimeout = 1, $logErrors = true)
{
    $result = [
        'success' => false,
        'data' => null,
        'http_code' => 0,
        'error' => ''
    ];

    // 记录开始请求的时间
    $startTime = microtime(true);

    // 初始化cURL
    $ch = curl_init($url);

    // 基本设置
    $options = [
        CURLOPT_RETURNTRANSFER => true,     // 返回结果而不是直接输出
        CURLOPT_TIMEOUT => $timeout,        // 总超时时间(秒)
        CURLOPT_CONNECTTIMEOUT => $connectTimeout, // 连接超时时间(秒)
        CURLOPT_SSL_VERIFYPEER => false,    // 不验证SSL证书
        CURLOPT_SSL_VERIFYHOST => false,    // 不验证主机名
        CURLOPT_FAILONERROR => false        // 即使HTTP状态码指示错误也返回内容
    ];

    // 如果有POST数据，设置为POST请求
    if (!empty($postData)) {
        $options[CURLOPT_POST] = true;
        $options[CURLOPT_POSTFIELDS] = is_array($postData) ? http_build_query($postData) : $postData;
        $options[CURLOPT_HTTPHEADER] = ['Content-Type: application/x-www-form-urlencoded'];
    }

    // 设置cURL选项
    curl_setopt_array($ch, $options);

    // 执行请求
    $response = curl_exec($ch);
    $result['http_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $requestTime = microtime(true) - $startTime;
    // 检查请求是否成功
    if ($response === false) {
        $result['error'] = curl_error($ch);
        if ($logErrors) {
            error_log(sprintf(
                "HTTP请求失败: %s, 错误: %s, 耗时: %.3f秒",
                $url,
                $result['error'],
                $requestTime
            ));
        }
    } else {
        $result['success'] = ($result['http_code'] >= 200 && $result['http_code'] < 300);
        $result['data'] = $response;

        if (!$result['success'] && $logErrors) {
            error_log(sprintf(
                "HTTP请求返回非成功状态码: %s, 状态码: %d, 耗时: %.3f秒",
                $url,
                $result['http_code'],
                $requestTime
            ));
        }
    }

    curl_close($ch);
    return $result;
}

/**
 * 通过API验证授权码
 * 
 * @param string $code 授权码
 * @return array|false 成功返回授权信息，失败返回false
 */
function verifyAuthCodeViaAPI($code)
{
    global $AUTH_API_BASE_URL;

    $domain = getCurrentDomain();
    $apiUrl = $AUTH_API_BASE_URL . '?action=verify_auth';

    // 生成当前时间戳
    $timestamp = time();

    // 附加一些防破解措施 - 使用时间戳参数生成token（与ajax.php一致）
    $secureToken = md5($code . date('Ymd', $timestamp) . $domain);

    try {
        // 准备请求数据
        $postData = [
            'auth_code' => $code,
            'domain' => $domain,
            'client_ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'verify_token' => $secureToken,
            'timestamp' => $timestamp
        ];

        // 使用封装的HTTP请求函数
        $response = httpRequest($apiUrl, $postData, 2, 1);
        // 检查是否请求成功 - 只有在API真正不可访问时才启用紧急授权
        if (!$response['success']) {
            // API不可访问或超时，记录警告并启用紧急授权
            error_log(sprintf(
                '授权验证警告: 无法连接到授权服务器，状态码: %d，错误: %s，启用紧急授权',
                $response['http_code'],
                $response['error']
            ));

            // 创建一个紧急授权
            return [
                'domain' => $domain,
                'expire_time' => time() + AUTH_EMERGENCY_EXPIRE,
                'is_trial' => false,
                'is_emergency' => true, // 标记为紧急授权
                'verifyAuthCodeViaAPI' => true
            ];
        }

        // 解析JSON响应
        $result = json_decode($response['data'], true);
        // 检查JSON解析是否成功 - JSON解析失败视为授权无效
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($result)) {
            error_log(sprintf(
                '授权验证失败: 无法解析API响应，JSON错误: %s，响应内容: %s',
                json_last_error_msg(),
                substr($response['data'], 0, 100) . (strlen($response['data']) > 100 ? '...' : '')
            ));
            return false;
        }

        // 检查响应格式是否正确 - 格式不正确视为授权无效
        if (!isset($result['code'])) {
            error_log('授权验证失败: API响应格式不正确，缺少code字段');
            return false;
        }

        // 检查响应状态
        if ($result['code'] !== 0) {
            // API正常返回了错误，需要区分错误类型
            $errorMsg = $result['msg'] ?? '未知错误';
            error_log('授权验证失败: ' . $errorMsg);
            // 如果是授权过期（code=1），返回特殊标识
            if ($result['code'] === 1) {
                return [
                    'error_type' => 'expired',
                    'message' => $errorMsg,
                    'is_trial' => isset($result['data']['is_trial']) ? (bool)$result['data']['is_trial'] : false,
                    'verifyAuthCodeViaAPI' => false
                ];
            }
            if ($result['code'] === 2) {
                return [
                    'error_type' => 'disabled',
                    'message' => $errorMsg,
                    'is_trial' => false,
                    'verifyAuthCodeViaAPI' => false
                ];
            }
            if ($result['code'] === 3) {
                return [
                    'error_type' => 'unauthorized',
                    'message' => $errorMsg,
                    'is_trial' => false,
                    'verifyAuthCodeViaAPI' => false
                ];
            }
            // 其他错误类型（授权码无效等）
            return false;
        }

        // 验证响应中是否包含必要的数据 - 数据不完整视为授权无效
        if (!isset($result['data']['domain'], $result['data']['expire_time'], $result['data']['is_trial'])) {
            error_log('授权验证失败: API响应数据不完整');
            return false;
        }

        // 返回授权信息
        return [
            'domain' => $result['data']['domain'],
            'expire_time' => strtotime($result['data']['expire_time']),
            'is_trial' => (bool)$result['data']['is_trial'],
            'is_emergency' => false // 正常授权
        ];
    } catch (Exception $e) {
        // 发生未捕获的异常通常也意味着API无法正常访问
        error_log('授权验证异常: ' . $e->getMessage() . '，启用紧急授权');

        // 发生异常也视为API不可用，启用紧急授权
        return [
            'domain' => $domain,
            'expire_time' => time() + AUTH_EMERGENCY_EXPIRE,
            'is_trial' => false,
            'is_emergency' => true
        ];
    }
}

/**
 * 检测是否存在调试器环境
 * 
 * @return bool 存在调试器返回true
 */
function checkDebugger()
{
    // 检测常见调试环境
    if (
        function_exists('xdebug_get_code_coverage') ||
        defined('PHPUNIT_COMPOSER_INSTALL') ||
        defined('PHPUNIT') ||
        getenv('XDEBUG_CONFIG') !== false
    ) {
        return true;
    }

    // 检测PHP调试扩展
    $extensions = get_loaded_extensions();
    $debugExtensions = ['xdebug', 'zend_debugger'];
    foreach ($debugExtensions as $ext) {
        if (in_array($ext, $extensions)) {
            return true;
        }
    }

    // 性能检测（用于识别断点调试）
    $startTime = microtime(true);
    for ($i = 0; $i < 100000; $i++) {
        $x = $i * $i;
    }
    $endTime = microtime(true);

    // 如果循环执行时间异常长，可能存在断点调试
    if (($endTime - $startTime) > 0.5) {
        return true;
    }

    return false;
}

/**
 * 获取域名的根域名
 * 例如：www.example.com 和 sub.example.com 都返回 example.com
 * 
 * @param string $domain 完整域名
 * @return string 根域名
 */
function getDomainRoot($domain)
{
    // 移除可能存在的端口号
    $domain = preg_replace('/:\d+$/', '', $domain);

    // 分割域名部分
    $parts = explode('.', $domain);
    $partsCount = count($parts);

    // 对于像 .co.uk 这样的特殊顶级域名，需要特殊处理
    if ($partsCount > 2) {
        // 检查是否是特殊顶级域名 (.co.uk, .com.cn 等)
        $specialTLDs = ['co.uk', 'com.cn', 'net.cn', 'org.cn', 'gov.cn', 'ac.cn', 'co.jp', 'co.nz', 'co.za'];
        $lastTwo = $parts[$partsCount - 2] . '.' . $parts[$partsCount - 1];

        if (in_array($lastTwo, $specialTLDs) && $partsCount >= 3) {
            return $parts[$partsCount - 3] . '.' . $lastTwo;
        } else {
            return $parts[$partsCount - 2] . '.' . $parts[$partsCount - 1];
        }
    }

    // 如果只有两段或更少，则返回整个域名
    return $domain;
}

/**
 * 显示授权错误信息
 *
 * @param string $message 错误信息
 */
function showAuthError($message)
{
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权验证失败</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .auth-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            animation: slideUp 0.6s ease-out;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .error-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: white;
            font-size: 36px;
        }
        .error-title {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            line-height: 1.4;
        }
        .error-message {
            color: #7f8c8d;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 32px;
        }
        .auth-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .auth-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        @media (max-width: 480px) {
            .auth-container { padding: 30px 20px; }
            .error-title { font-size: 20px; }
            .error-message { font-size: 14px; }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="error-icon">⚠️</div>
        <h1 class="error-title">' . htmlspecialchars($message) . '</h1>
        <p class="error-message">请联系管理员获取授权，或点击下方按钮进入授权系统获取有效的授权码。</p>
        <a href="' . AUTH_WEB_URL . '" target="_blank" class="auth-button">进入授权系统</a>
    </div>
</body>
</html>';
    exit;
}

/**
 * 显示续费提示
 *
 * @param string $message 提示信息
 */
function showAuthRenewal($message)
{
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权已过期</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .renewal-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 520px;
            width: 100%;
            text-align: center;
            animation: slideUp 0.6s ease-out;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .renewal-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff9a56, #ff6b6b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            color: white;
            font-size: 36px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .renewal-title {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            line-height: 1.4;
        }
        .renewal-message {
            color: #7f8c8d;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 32px;
        }
        .renewal-button {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b, #ff5722);
            color: white;
            text-decoration: none;
            padding: 16px 36px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            position: relative;
            overflow: hidden;
        }
        .renewal-button:before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .renewal-button:hover:before {
            left: 100%;
        }
        .renewal-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
        }
        .features {
            margin-top: 32px;
            text-align: left;
            background: #f8f9fa;
            padding: 24px;
            border-radius: 12px;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #495057;
            font-size: 14px;
        }
        .feature-item:last-child { margin-bottom: 0; }
        .feature-icon {
            color: #28a745;
            margin-right: 12px;
            font-weight: bold;
        }
        @media (max-width: 480px) {
            .renewal-container { padding: 30px 20px; }
            .renewal-title { font-size: 20px; }
            .renewal-message { font-size: 14px; }
            .features { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="renewal-container">
        <div class="renewal-icon">⏰</div>
        <h1 class="renewal-title">' . htmlspecialchars($message) . '</h1>
        <p class="renewal-message">您的授权已过期，请及时续费以继续享受完整的服务功能。</p>
        <a href="' . AUTH_WEB_URL . '" target="_blank" class="renewal-button">立即续费</a>

        <div class="features">
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>续费后立即恢复所有功能</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>享受技术支持和更新服务</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>数据安全保障和备份服务</span>
            </div>
        </div>
    </div>
</body>
</html>';
    exit;
}

/**
 * 显示试用选项
 * 
 * @param string $message 提示信息
 */
function showAuthTrialOption($message)
{
    global $AUTH_API_BASE_URL;
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>免费试用</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .trial-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            padding: 50px;
            max-width: 600px;
            width: 100%;
            text-align: center;
            animation: slideUp 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }
        .trial-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 100%;
            animation: gradientShift 3s ease infinite;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        .trial-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            color: white;
            font-size: 48px;
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .trial-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.3;
        }
        .trial-subtitle {
            color: #7f8c8d;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 40px;
        }
        .trial-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
            text-align: left;
        }
        .feature-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            transition: transform 0.3s ease;
        }
        .feature-item:hover {
            transform: translateY(-2px);
        }
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 18px;
        }
        .feature-text {
            color: #495057;
            font-size: 14px;
            font-weight: 500;
        }
        #trialBtn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            position: relative;
            overflow: hidden;
        }
        #trialBtn:before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        #trialBtn:hover:before {
            left: 100%;
        }
        #trialBtn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(40, 167, 69, 0.4);
        }
        #trialBtn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        #trialStatus {
            margin-top: 30px;
            padding: 20px;
            border-radius: 12px;
            display: none;
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .status-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-loading {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        @media (max-width: 600px) {
            .trial-container {
                padding: 30px 25px;
                margin: 10px;
            }
            .trial-title { font-size: 24px; }
            .trial-subtitle { font-size: 16px; }
            .trial-features { grid-template-columns: 1fr; }
            #trialBtn {
                padding: 16px 32px;
                font-size: 16px;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="trial-container">
        <div class="trial-icon">🚀</div>
        <h1 class="trial-title">' . htmlspecialchars($message) . '</h1>
        <p class="trial-subtitle">立即开始 ' . AUTH_TRIAL_DAYS . ' 天免费试用，体验完整功能，无需任何费用</p>

        <div class="trial-features">
            <div class="feature-item">
                <div class="feature-icon">✨</div>
                <div class="feature-text">完整功能体验<br><small>试用期内享受所有功能</small></div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">🛡️</div>
                <div class="feature-text">数据安全保障<br><small>专业级数据保护</small></div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">即时激活<br><small>一键开启试用</small></div>
            </div>
        </div>

        <button id="trialBtn" onclick="activateTrial()">
            开始 ' . AUTH_TRIAL_DAYS . ' 天免费试用
        </button>

        <div id="trialStatus"></div>
    </div>';
    echo '<script>
    function activateTrial() {
        // 获取当前域名
        var domain = window.location.hostname;

        // 显示加载状态
        var btn = document.getElementById("trialBtn");
        var statusDiv = document.getElementById("trialStatus");
        btn.disabled = true;
        btn.innerHTML = "⏳ 正在激活...";
        statusDiv.style.display = "block";
        statusDiv.className = "status-loading";
        statusDiv.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center;">
                <div style="
                    width: 20px;
                    height: 20px;
                    border: 2px solid #bee5eb;
                    border-top: 2px solid #0c5460;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin-right: 10px;
                "></div>
                <span>正在激活试用，请稍候...</span>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;

        // 准备参数
        var timestamp = Math.floor(Date.now() / 1000);
        var postData = "domain=" + encodeURIComponent(domain) + "&timestamp=" + timestamp;
        
        // 发送AJAX请求
        var xhr = new XMLHttpRequest();
        xhr.open("POST", "' . $AUTH_API_BASE_URL . '?action=activate_trial", true);
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        xhr.timeout = 10000; // 10秒超时
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.code === 0) {
                        statusDiv.className = "status-success";
                        statusDiv.innerHTML = `
                            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                                <div style="
                                    width: 50px;
                                    height: 50px;
                                    background: #28a745;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: white;
                                    font-size: 24px;
                                    animation: successPulse 0.6s ease-out;
                                ">✓</div>
                            </div>
                            <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">🎉 试用激活成功！</div>
                            <div style="font-size: 14px; opacity: 0.8;">您已获得 ' . AUTH_TRIAL_DAYS . ' 天的试用期，页面将在 <span id="countdown">3</span> 秒后自动刷新</div>
                            <style>
                                @keyframes successPulse {
                                    0% { transform: scale(0); }
                                    50% { transform: scale(1.2); }
                                    100% { transform: scale(1); }
                                }
                            </style>
                        `;

                        // 倒计时刷新
                        var countdown = 3;
                        var countdownElement = document.getElementById("countdown");
                        var countdownInterval = setInterval(function() {
                            countdown--;
                            if (countdownElement) {
                                countdownElement.textContent = countdown;
                            }
                            if (countdown <= 0) {
                                clearInterval(countdownInterval);
                                window.location.reload();
                            }
                        }, 1000);
                    } else {
                        btn.disabled = false;
                        btn.innerHTML = "🔄 重试激活";
                        statusDiv.className = "status-error";
                        statusDiv.innerHTML = `
                            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                                <div style="
                                    width: 40px;
                                    height: 40px;
                                    background: #dc3545;
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: white;
                                    font-size: 20px;
                                ">⚠</div>
                            </div>
                            <div style="font-weight: 600; margin-bottom: 8px;">激活失败</div>
                            <div style="font-size: 14px;">${response.msg || "未知错误"}</div>
                        `;
                    }
                } catch (e) {
                    btn.disabled = false;
                    btn.innerHTML = "🔄 重试激活";
                    statusDiv.className = "status-error";
                    statusDiv.innerHTML = `
                        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                            <div style="
                                width: 40px;
                                height: 40px;
                                background: #dc3545;
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                font-size: 20px;
                            ">⚠</div>
                        </div>
                        <div style="font-weight: 600; margin-bottom: 8px;">请求处理失败</div>
                        <div style="font-size: 14px;">请检查网络连接后重试</div>
                    `;
                    console.error("解析响应失败:", e);
                }
            }
        };
        
        xhr.ontimeout = function() {
            btn.disabled = false;
            btn.innerHTML = "🔄 重试激活";
            statusDiv.className = "status-error";
            statusDiv.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        background: #dc3545;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 20px;
                    ">⏰</div>
                </div>
                <div style="font-weight: 600; margin-bottom: 8px;">请求超时</div>
                <div style="font-size: 14px;">请检查网络连接后重试</div>
            `;
        };

        xhr.onerror = function() {
            btn.disabled = false;
            btn.innerHTML = "🔄 重试激活";
            statusDiv.className = "status-error";
            statusDiv.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                    <div style="
                        width: 40px;
                        height: 40px;
                        background: #dc3545;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-size: 20px;
                    ">🌐</div>
                </div>
                <div style="font-weight: 600; margin-bottom: 8px;">网络连接失败</div>
                <div style="font-size: 14px;">请检查您的网络连接</div>
            `;
        };

        // 发送请求
        xhr.send(postData);
    }
    </script>
</body>
</html>';
    exit;
}

/**
 * 显示混淆的错误信息（防止提供有用的调试信息给攻击者）
 * 
 * @param string $message 内部错误信息
 */
function showObfuscatedError($message)
{
    // 记录真实错误到日志
    error_log('授权验证安全警告: ' . $message);

    // 向用户显示模糊错误
    echo '<div style="padding:20px;background:#f8f8f8;border:1px solid #ddd;margin:20px;border-radius:5px;font-family:Arial;">';
    echo '<h2 style="color:#e74c3c;">系统错误</h2>';
    echo '<p>程序运行环境异常，请联系管理员。</p>';
    echo '<p style="color:#999;font-size:12px;">错误码: ' . substr(md5(rand(10000, 99999)), 0, 8) . '</p>';
    echo '</div>';
    exit;
}

/**
 * 检查域名是否可以试用
 * 
 * @param string $domain 要检查的域名
 * @return bool 如果域名可以试用返回true，否则返回false
 */
function canDomainTrial($domain)
{
    global $AUTH_API_BASE_URL;

    try {
        // 准备请求数据
        $postData = [
            'domain' => $domain,
            'check_trial' => true,
            'timestamp' => time()
        ];

        // 使用封装的HTTP请求函数
        $apiUrl = $AUTH_API_BASE_URL . '?action=check_domain';
        $response = httpRequest($apiUrl, $postData, AUTH_API_TIMEOUT, 2);

        if (!$response['success']) {
            // 如果无法连接到授权服务器，默认允许试用
            error_log(sprintf(
                '试用检查: 无法连接到授权服务器，状态码: %d，错误: %s，默认允许试用',
                $response['http_code'],
                $response['error']
            ));
            return true;
        }

        // 解析响应
        $result = json_decode($response['data'], true);
        if (!is_array($result)) {
            error_log(sprintf(
                '试用检查: 授权服务器响应格式错误，响应内容: %s，默认允许试用',
                substr($response['data'], 0, 100) . (strlen($response['data']) > 100 ? '...' : '')
            ));
            return true;
        }

        // 检查响应状态
        if (!isset($result['code']) || $result['code'] !== 1) {
            error_log(sprintf(
                '试用检查: API返回错误，错误码: %d, 错误信息: %s，默认允许试用',
                $result['code'] ?? -1,
                $result['msg'] ?? '未知错误'
            ));
            return true;
        }

        // 检查域名是否已经使用过试用版
        // 如果响应中包含can_trial字段为true，则表示可以试用
        if (isset($result['data']['can_trial'])) {
            return (bool)$result['data']['can_trial'];
        } else {
            // 如果没有明确指出不能试用，默认允许
            return true;
        }
    } catch (Exception $e) {
        error_log('检查试用状态异常: ' . $e->getMessage() . '，默认允许试用');
        // 出现异常时默认允许试用
        return true;
    }
}

/**
 * 从缓存加载授权信息
 * 
 * @param string $domain 域名
 * @param string $authCode 授权码
 * @return array|false 成功返回缓存的授权信息，失败或过期返回false
 */
function loadAuthCache($domain, $authCode)
{
    $cacheFile = generateCacheFilename($domain, $authCode);

    // 检查缓存文件是否存在
    if (!file_exists($cacheFile)) {
        return false;
    }

    // 读取缓存文件
    $content = @file_get_contents($cacheFile);
    if ($content === false) {
        return false;
    }

    try {
        // 解密缓存内容
        $decrypted = decryptCacheData($content);
        if ($decrypted === false) {
            // 解密失败，删除无效缓存
            @unlink($cacheFile);
            return false;
        }

        // 解析缓存内容
        $cacheData = json_decode($decrypted, true);
        if (!is_array($cacheData)) {
            // 缓存数据无效
            @unlink($cacheFile); // 删除无效缓存
            return false;
        }

        // 检查缓存是否已过期（缓存自身的过期时间）
        if (!isset($cacheData['cache_expire']) || time() > $cacheData['cache_expire']) {
            // 缓存已过期
            @unlink($cacheFile); // 删除过期缓存
            return false;
        }

        // 校验缓存的完整性
        $expectedChecksum = generateCacheChecksum($domain, $authCode, $cacheData);
        if (!isset($cacheData['checksum']) || $cacheData['checksum'] !== $expectedChecksum) {
            error_log('授权缓存验证失败: 数据完整性校验不通过');
            @unlink($cacheFile); // 删除被篡改的缓存
            return false;
        }

        // 验证环境指纹 - 检查是否在同一环境使用
        $currentFingerprint = generateEnvironmentFingerprint($domain);
        if (!isset($cacheData['env_fingerprint']) || !verifyEnvironmentFingerprint($currentFingerprint, $cacheData['env_fingerprint'])) {
            error_log('授权缓存验证失败: 环境指纹不匹配');
            return false;
        }

        // 返回授权信息
        return [
            'domain' => $cacheData['domain'],
            'expire_time' => $cacheData['expire_time'],
            'is_trial' => $cacheData['is_trial']
        ];
    } catch (Exception $e) {
        error_log('授权缓存读取异常: ' . $e->getMessage());
        return false;
    }
}

/**
 * 保存授权信息到缓存
 * 
 * @param string $domain 域名
 * @param string $authCode 授权码
 * @param array $authInfo 授权信息
 * @return bool 保存成功返回true，失败返回false
 */
function saveAuthCache($domain, $authCode, $authInfo)
{
    $cacheFile = generateCacheFilename($domain, $authCode);

    try {
        // 生成环境指纹
        $envFingerprint = generateEnvironmentFingerprint($domain);

        // 准备缓存数据
        $cacheData = [
            'domain' => $authInfo['domain'],
            'expire_time' => $authInfo['expire_time'],
            'is_trial' => $authInfo['is_trial'],
            'cache_expire' => time() + AUTH_CACHE_EXPIRE, // 缓存过期时间
            'created_at' => time(),
            'env_fingerprint' => $envFingerprint
        ];

        // 添加安全校验
        $cacheData['checksum'] = generateCacheChecksum($domain, $authCode, $cacheData);

        // 将缓存数据序列化为JSON
        $jsonData = json_encode($cacheData);

        // 加密缓存数据
        $encrypted = encryptCacheData($jsonData);
        if ($encrypted === false) {
            error_log('授权缓存加密失败');
            return false;
        }

        // 写入加密后的缓存文件
        $result = @file_put_contents($cacheFile, $encrypted);

        return ($result !== false);
    } catch (Exception $e) {
        error_log('授权缓存保存异常: ' . $e->getMessage());
        return false;
    }
}

/**
 * 删除授权缓存
 * 
 * @param string $domain 域名
 * @param string $authCode 授权码
 * @return bool 删除成功返回true
 */
function deleteAuthCache($domain, $authCode)
{
    $cacheFile = generateCacheFilename($domain, $authCode);

    if (file_exists($cacheFile)) {
        return @unlink($cacheFile);
    }

    return true; // 文件不存在也算删除成功
}

/**
 * 生成缓存文件名
 * 
 * @param string $domain 域名
 * @param string $authCode 授权码
 * @return string 缓存文件的完整路径
 */
function generateCacheFilename($domain, $authCode)
{
    $safeDomain = preg_replace('/[^a-z0-9]/i', '_', $domain);
    $hash = md5($authCode . $safeDomain);
    return AUTH_CACHE_DIR . '/auth_' . $hash . '.cache';
}

/**
 * 生成环境指纹
 * 用于防止缓存在不同环境之间移植
 * 
 * @param string $domain 域名
 * @return string 环境指纹
 */
function generateEnvironmentFingerprint($domain)
{
    $fingerprint = [
        'domain' => $domain,
        'ip' => $_SERVER['SERVER_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '',
        'server_name' => $_SERVER['SERVER_NAME'] ?? '',
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? '',
        'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? '',
        'php_version' => PHP_VERSION,
        // 添加一些相对稳定的环境特征，但排除易变因素如时间戳
    ];

    // 排序以确保一致性
    ksort($fingerprint);

    // 使用密钥生成HMAC
    return hash_hmac('sha256', json_encode($fingerprint), AUTH_CACHE_ENCRYPT_KEY);
}

/**
 * 验证环境指纹
 * 
 * @param string $currentFingerprint 当前环境指纹
 * @param string $storedFingerprint 存储的环境指纹
 * @return bool 如果指纹匹配返回true，否则返回false
 */
function verifyEnvironmentFingerprint($currentFingerprint, $storedFingerprint)
{
    // 简单的字符串比较 - 任何更改都会使缓存失效
    return hash_equals($currentFingerprint, $storedFingerprint);
}

/**
 * 生成缓存校验和
 *
 * @param string $domain 域名
 * @param string $authCode 授权码
 * @param array $cacheData 缓存数据
 * @return string 缓存校验和
 */
function generateCacheChecksum($domain, $authCode, $cacheData)
{
    // 包含缓存中的域名信息，防止域名篡改
    $checksumData = [
        'input_domain' => $domain,
        'cache_domain' => $cacheData['domain'] ?? '',
        'auth_code' => $authCode,
        'expire_time' => $cacheData['expire_time'] ?? 0,
        'is_trial' => $cacheData['is_trial'] ?? false,
        'cache_expire' => $cacheData['cache_expire'] ?? 0,
        'created_at' => $cacheData['created_at'] ?? 0,
        'env_fingerprint' => $cacheData['env_fingerprint'] ?? ''
    ];

    // 使用更安全的HMAC算法生成校验和
    return hash_hmac('sha256', json_encode($checksumData), AUTH_CACHE_ENCRYPT_KEY);
}

/**
 * 解密缓存数据
 * 
 * @param string $encryptedData 加密后的缓存数据
 * @return string|false 解密后的缓存数据，失败返回false
 */
function decryptCacheData($encryptedData)
{
    $ivLen = openssl_cipher_iv_length('AES-256-CBC');
    if (strlen($encryptedData) <= $ivLen) {
        return false;
    }

    // 提取IV和加密数据
    $iv = substr($encryptedData, 0, $ivLen);
    $ciphertext = substr($encryptedData, $ivLen);

    return openssl_decrypt($ciphertext, 'AES-256-CBC', AUTH_CACHE_ENCRYPT_KEY, 0, $iv);
}

/**
 * 加密缓存数据
 * 
 * @param string $plainData 明文缓存数据
 * @return string|false 加密后的缓存数据，失败返回false
 */
function encryptCacheData($plainData)
{
    // 生成随机IV
    $ivLen = openssl_cipher_iv_length('AES-256-CBC');
    $iv = openssl_random_pseudo_bytes($ivLen);

    $ciphertext = openssl_encrypt($plainData, 'AES-256-CBC', AUTH_CACHE_ENCRYPT_KEY, 0, $iv);
    if ($ciphertext === false) {
        return false;
    }

    // 将IV和加密数据组合在一起
    return $iv . $ciphertext;
}

/**
 * 获取当前域名
 * 
 * @return string 当前域名（不含端口号）
 */
function getCurrentDomain()
{
    $domain = $_SERVER['HTTP_HOST'] ?? 'localhost';
    // 移除端口号
    $hostParts = explode(':', $domain);
    return $hostParts[0];
}

/**
 * 检查是否处于开发模式
 * 
 * @param string $domain 当前域名
 * @return bool 是否处于开发模式
 */
function isDevMode($domain)
{
    // 开发模式只能在特定条件下启用
    if (file_exists(__DIR__ . "/../../dev_mode.php") && in_array($domain, ['localhost', '127.0.0.1'])) {
        // 加载开发模式配置
        $devConfig = include(__DIR__ . "/../../dev_mode.php");
        if (
            isset($devConfig['enabled']) && $devConfig['enabled'] === true &&
            isset($devConfig['dev_key']) && $devConfig['dev_key'] === AUTH_DEV_MODE_KEY
        ) {
            echo '<div style="
                position: fixed;
                bottom: 20px;
                left: 20px;
                z-index: 9999;
                background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
                border: none;
                border-radius: 12px;
                padding: 12px 16px;
                box-shadow: 0 8px 32px rgba(127, 205, 205, 0.3);
                font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;
                animation: slideInLeft 0.5s ease-out;
                backdrop-filter: blur(10px);
            ">
                <div style="
                    display: flex;
                    align-items: center;
                    color: #2d3436;
                    font-size: 13px;
                    font-weight: 500;
                ">
                    <span style="
                        font-size: 16px;
                        margin-right: 8px;
                        animation: bounce 1s infinite;
                    ">🔧</span>
                    <span style="font-weight: 600;">开发者模式</span>
                    <span style="margin-left: 8px; opacity: 0.7;">授权验证已跳过</span>
                </div>
                <style>
                    @keyframes slideInLeft {
                        from { transform: translateX(-100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes bounce {
                        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                        40% { transform: translateY(-4px); }
                        60% { transform: translateY(-2px); }
                    }
                    @media (max-width: 480px) {
                        div[style*=\"position: fixed\"][style*=\"bottom: 20px\"] {
                            position: fixed !important;
                            bottom: 10px !important;
                            left: 10px !important;
                            right: 10px !important;
                        }
                    }
                </style>
            </div>';
            return true; // 仅在配置了正确的开发密钥时跳过验证
        }
    }
    return false;
}

/**
 * 处理空授权码情况
 * 
 * @param string $domain 当前域名
 * @return bool 总是返回false（未授权）
 */
function handleEmptyAuthCode($domain)
{
    // 检查该域名是否可以试用
    if (canDomainTrial($domain)) {
        showAuthTrialOption("程序未授权，您可以先试用");
    } else {
        showAuthError("程序未授权，请先获取授权");
    }
    return false;
}

/**
 * 处理缓存的授权信息
 *
 * @param string $domain 当前域名
 * @param string $authcode 授权码
 * @param array $cacheInfo 缓存的授权信息
 * @return bool 授权有效返回true，否则返回false
 */
function handleCachedAuth($domain, $authcode, $cacheInfo)
{
    // 1. 验证缓存中的域名与当前域名是否匹配（防止缓存替换攻击）
    if (!isset($cacheInfo['domain']) || !isDomainMatched($domain, $cacheInfo['domain'])) {
        error_log(sprintf(
            '授权缓存验证失败: 域名不匹配，当前域名: %s，缓存域名: %s',
            $domain,
            $cacheInfo['domain'] ?? 'null'
        ));
        // 删除域名不匹配的缓存
        deleteAuthCache($domain, $authcode);
        showAuthError("授权码与当前域名不匹配");
        return false;
    }

    // 2. 检查缓存中的授权是否过期
    if (time() > $cacheInfo['expire_time']) {
        // 判断是否为试用版授权
        if ($cacheInfo['is_trial']) {
            showAuthError("试用期已结束，请购买正式授权");
        } else {
            showAuthRenewal("授权已过期，请续费后继续使用");
        }
        // 删除过期的缓存
        deleteAuthCache($domain, $authcode);
        return false;
    }

    // 3. 缓存中的授权有效
    return true;
}

/**
 * 处理无效授权码情况
 * 
 * @param string $domain 当前域名
 * @return bool 总是返回false（未授权）
 */
function handleInvalidAuthCode($domain)
{
    // 检查该域名是否可以试用
    if (canDomainTrial($domain)) {
        showAuthTrialOption("授权码无效，您可以先试用");
    } else {
        showAuthError("授权码无效，请联系管理员获取有效授权");
    }
    return false;
}

/**
 * 检查域名是否匹配
 * 
 * @param string $currentDomain 当前域名
 * @param string $authDomain 授权域名
 * @return bool 域名匹配返回true，否则返回false
 */
function isDomainMatched($currentDomain, $authDomain)
{
    // 支持子域名 - 获取根域名进行比较
    $currentDomainRoot = getDomainRoot($currentDomain);
    $authDomainRoot = getDomainRoot($authDomain);

    return $currentDomainRoot === $authDomainRoot;
}

/**
 * 检查授权是否过期
 * 
 * @param array $authInfo 授权信息
 * @return bool 授权过期返回true，否则返回false
 */
function isAuthExpired($authInfo)
{
    return time() > $authInfo['expire_time'];
}

/**
 * 处理授权过期情况
 * 
 * @param array $authInfo 授权信息
 * @return bool 总是返回false（授权已过期）
 */
function handleExpiredAuth($authInfo)
{
    // 判断是否为试用版授权
    if ($authInfo['is_trial']) {
        showAuthError("试用期已结束，请购买正式授权");
    } else {
        showAuthRenewal("授权已过期，请续费后继续使用");
    }
    return false;
}

/**
 * 显示紧急授权提示
 */
function showEmergencyNotice()
{
    echo '<div style="
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
        border: none;
        border-radius: 12px;
        padding: 16px 20px;
        box-shadow: 0 8px 32px rgba(253, 203, 110, 0.3);
        font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;
        animation: slideInRight 0.5s ease-out, pulse 2s infinite 1s;
        backdrop-filter: blur(10px);
    ">
        <div style="
            display: flex;
            align-items: center;
            color: #2d3436;
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
        ">
            <span style="
                font-size: 20px;
                margin-right: 12px;
            ">⚡</span>
            <div>
                <div style="font-weight: 600; margin-bottom: 4px;">紧急授权模式</div>
                <div style="font-size: 12px; opacity: 0.8;">授权服务器暂时不可用，系统已启用临时授权</div>
            </div>
        </div>
        <style>
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes pulse {
                0%, 100% { box-shadow: 0 8px 32px rgba(253, 203, 110, 0.3); }
                50% { box-shadow: 0 8px 32px rgba(253, 203, 110, 0.6); }
            }
            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            @media (max-width: 480px) {
                div[style*=\"position: fixed\"] {
                    position: fixed !important;
                    top: 10px !important;
                    left: 10px !important;
                    right: 10px !important;
                    max-width: none !important;
                }
            }
        </style>
    </div>';
}
