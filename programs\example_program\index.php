<?php
/**
 * 示例程序入口文件
 */
require_once 'includes/authcode.php';

// 检查授权
if (!checkAuth()) {
    exit;
}

// 程序正常内容
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>授权系统示例程序</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #f9f9f9; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .success { color: #4CAF50; }
        .info { margin-top: 20px; background: #e9f7ef; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>示例程序</h1>
        <p class="success">恭喜，您已成功授权！</p>
        
        <div class="info">
            <h3>授权信息</h3>
            <p>域名：<?php echo $_SERVER['HTTP_HOST']; ?></p>
            <p>授权码：已验证</p>
            <p>状态：正常</p>
        </div>
    </div>
</body>
</html>