# 域名授权系统试用机制设计

创建时间: [2024-08-19 12:15:00 +08:00]
创建者: 系统架构师(AR)
最后更新: [2024-08-19 12:15:00 +08:00]

## 试用功能概述

试用功能允许用户在购买正式授权前，先试用系统3天。这将提高用户转化率，让用户能够在决定购买前体验产品功能。

## 试用机制设计

### 1. 数据库设计

在`domains`表中添加试用相关字段：

```sql
ALTER TABLE `domains` ADD COLUMN `is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否试用状态:0否,1是';
ALTER TABLE `domains` ADD COLUMN `trial_start_time` datetime DEFAULT NULL COMMENT '试用开始时间';
```

在`downloads`表中添加试用标记：

```sql
ALTER TABLE `downloads` ADD COLUMN `is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否试用下载:0否,1是';
```

### 2. 试用激活流程

```
用户输入域名 → 查询未授权 → 显示试用选项 → 用户点击"立即试用" →
系统激活试用 → 记录试用开始时间 → 允许下载试用版 → 
3天后试用到期 → 提示购买正式授权
```

### 3. 域名授权验证逻辑

```php
/**
 * 验证域名授权状态
 * @param string $domain 要验证的域名
 * @return array 验证结果，包含状态和相关信息
 */
function verifyDomainAuth($domain) {
    global $db;
    
    // 净化域名输入
    $domain = filter_var($domain, FILTER_SANITIZE_STRING);
    
    // 查询数据库
    $stmt = $db->prepare("SELECT * FROM domains WHERE domain = ? LIMIT 1");
    $stmt->bind_param("s", $domain);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // 域名不存在，可以提供试用选项
        return [
            'status' => false,
            'message' => '域名未授权',
            'can_trial' => true
        ];
    }
    
    $domainData = $result->fetch_assoc();
    
    // 如果是正式授权
    if ($domainData['status'] == 1 && $domainData['is_trial'] == 0) {
        // 检查是否过期
        if (!empty($domainData['expire_time'])) {
            $expireTime = strtotime($domainData['expire_time']);
            if (time() > $expireTime) {
                return [
                    'status' => false,
                    'message' => '域名授权已过期',
                    'expire_time' => $domainData['expire_time'],
                    'can_trial' => true // 过期后可以再次试用
                ];
            }
        }
        
        return [
            'status' => true,
            'message' => '域名已授权',
            'domain_data' => $domainData,
            'is_trial' => false
        ];
    }
    
    // 如果是试用状态
    if ($domainData['status'] == 1 && $domainData['is_trial'] == 1) {
        // 检查试用是否过期（3天）
        $trialStart = strtotime($domainData['trial_start_time']);
        $trialEnd = $trialStart + (3 * 24 * 60 * 60); // 3天后
        
        if (time() > $trialEnd) {
            return [
                'status' => false,
                'message' => '试用期已结束',
                'trial_end' => date('Y-m-d H:i:s', $trialEnd),
                'can_renew' => false // 试用结束后不能再次试用
            ];
        }
        
        // 计算剩余试用天数
        $remainingSeconds = $trialEnd - time();
        $remainingDays = ceil($remainingSeconds / (24 * 60 * 60));
        
        return [
            'status' => true,
            'message' => '域名在试用期内',
            'domain_data' => $domainData,
            'is_trial' => true,
            'trial_end' => date('Y-m-d H:i:s', $trialEnd),
            'remaining_days' => $remainingDays
        ];
    }
    
    // 域名被禁用
    return [
        'status' => false,
        'message' => '域名已被禁用',
        'can_trial' => false
    ];
}
```

### 4. 试用激活API接口

```php
case 'activate_trial':
    // 获取参数
    $domain = $_POST['domain'] ?? '';
    
    // 验证域名格式
    if (!isValidDomain($domain)) {
        $response = new ApiResponse(1, '域名格式不正确');
        echo $response->toJson();
        exit;
    }
    
    // 检查域名是否存在
    $stmt = $db->prepare("SELECT * FROM domains WHERE domain = ? LIMIT 1");
    $stmt->bind_param("s", $domain);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $now = date('Y-m-d H:i:s');
    
    if ($result->num_rows === 0) {
        // 域名不存在，创建新记录并设为试用状态
        $stmt = $db->prepare("INSERT INTO domains (domain, auth_code, status, is_trial, trial_start_time, create_time, update_time) VALUES (?, '', 1, 1, ?, ?, ?)");
        $stmt->bind_param("ssss", $domain, $now, $now, $now);
        $stmt->execute();
    } else {
        // 域名存在，更新为试用状态
        $domainData = $result->fetch_assoc();
        
        // 如果已经是正式授权且未过期，不允许变为试用
        if ($domainData['status'] == 1 && $domainData['is_trial'] == 0) {
            if (empty($domainData['expire_time']) || strtotime($domainData['expire_time']) > time()) {
                $response = new ApiResponse(1, '该域名已经正式授权，无需试用');
                echo $response->toJson();
                exit;
            }
        }
        
        // 如果已经试用过，不允许再次试用
        if ($domainData['is_trial'] == 1) {
            $trialStart = strtotime($domainData['trial_start_time']);
            $trialEnd = $trialStart + (3 * 24 * 60 * 60); // 3天后
            
            if (time() <= $trialEnd) {
                $remainingSeconds = $trialEnd - time();
                $remainingDays = ceil($remainingSeconds / (24 * 60 * 60));
                
                $response = new ApiResponse(1, "该域名已在试用中，剩余{$remainingDays}天试用期");
                echo $response->toJson();
                exit;
            }
            
            // 试用已过期但不允许再次试用
            $response = new ApiResponse(1, '该域名已经使用过试用期，无法再次试用');
            echo $response->toJson();
            exit;
        }
        
        // 更新为试用状态
        $stmt = $db->prepare("UPDATE domains SET status = 1, is_trial = 1, trial_start_time = ?, update_time = ? WHERE domain = ?");
        $stmt->bind_param("sss", $now, $now, $domain);
        $stmt->execute();
    }
    
    // 返回成功响应
    $trialEnd = date('Y-m-d H:i:s', strtotime('+3 days', strtotime($now)));
    $response = new ApiResponse(0, '试用激活成功', [
        'domain' => $domain,
        'trial_start' => $now,
        'trial_end' => $trialEnd,
        'remaining_days' => 3
    ]);
    echo $response->toJson();
    break;
```

### 5. 试用授权码生成

针对试用状态，生成特殊的试用授权码：

```php
/**
 * 生成试用授权码
 * @param string $domain 域名
 * @param string $expireTime 过期时间 (Y-m-d H:i:s 格式)
 * @return string 生成的授权码
 */
function generateTrialAuthCode($domain, $expireTime) {
    // 基本授权码生成
    $baseCode = generateAuthCode($domain, $expireTime);
    
    // 添加试用标记
    $trialCode = 'TRIAL_' . $baseCode;
    
    return $trialCode;
}
```

### 6. 前端UI设计

试用功能的前端界面元素：

```html
<!-- 域名未授权时，显示试用选项 -->
<div id="trialOption" style="display:none; margin-top: 15px;">
  <div class="layui-bg-blue" style="padding: 10px;">
    您可以免费试用3天，试用期结束后需要购买授权。
  </div>
  <div style="margin-top: 10px;">
    <button type="button" class="layui-btn layui-btn-normal" id="activateTrial">立即试用</button>
  </div>
</div>

<!-- 试用中状态显示 -->
<div id="trialStatus" style="display:none; margin-top: 15px;">
  <div class="layui-bg-orange" style="padding: 10px;">
    当前为试用状态，剩余 <b id="remainingDays">0</b> 天试用期。
  </div>
  <div style="margin-top: 10px;">
    试用开始时间: <span id="trialStart"></span><br>
    试用结束时间: <span id="trialEnd"></span>
  </div>
</div>
```

对应的JavaScript处理：

```javascript
// 在域名查询成功回调中添加
if(!res.data.is_auth && res.data.can_trial) {
  $('#trialOption').show();
} else if(res.data.is_auth && res.data.is_trial) {
  $('#trialStatus').show();
  $('#remainingDays').text(res.data.remaining_days);
  $('#trialStart').text(res.data.domain_data.trial_start_time);
  $('#trialEnd').text(res.data.trial_end);
}

// 激活试用按钮点击处理
$('#activateTrial').on('click', function(){
  var domain = $('input[name="domain"]').val();
  
  // 显示加载中
  var loadIndex = layer.load(2);
  
  // 发送AJAX请求
  $.ajax({
    url: 'api/ajax.php?action=activate_trial',
    type: 'POST',
    data: {domain: domain},
    dataType: 'json',
    success: function(res){
      layer.close(loadIndex);
      
      if(res.code === 0){
        // 激活成功，显示试用信息
        $('#trialOption').hide();
        $('#trialStatus').show();
        $('#remainingDays').text(res.data.remaining_days);
        $('#trialStart').text(res.data.trial_start);
        $('#trialEnd').text(res.data.trial_end);
        
        // 刷新查询结果
        $('#checkDomain').click();
      } else {
        layer.msg(res.msg);
      }
    },
    error: function(){
      layer.close(loadIndex);
      layer.msg('网络错误，请重试');
    }
  });
});
```

### 7. 试用下载处理

在下载处理函数中需要处理试用状态：

```php
// 处理程序下载时增加试用判断
function processProgramDownload($domain, $programId) {
    // ...现有代码...
    
    // 生成授权码（根据是否为试用状态决定授权码类型和过期时间）
    $expireTime = $authResult['is_trial'] 
        ? date('Y-m-d H:i:s', strtotime('+3 days')) 
        : ($authResult['domain_data']['expire_time'] ?? date('Y-m-d H:i:s', strtotime('+1 year')));
    
    $authCode = $authResult['is_trial'] 
        ? generateTrialAuthCode($domain, $expireTime) 
        : generateAuthCode($domain, $expireTime);
    
    // ...现有代码...
    
    // 记录下载信息时标记是否为试用下载
    $isTrial = $authResult['is_trial'] ? 1 : 0;
    $stmt = $db->prepare("INSERT INTO downloads (domain, auth_code, download_time, ip, program_id, is_trial) VALUES (?, ?, NOW(), ?, ?, ?)");
    $stmt->bind_param("sssii", $domain, $authCode, $ip, $programId, $isTrial);
    
    // ...剩余代码...
}
```

## 试用功能优势

1. **增加用户转化率**
   - 降低用户决策门槛，先试用后购买
   - 提供无风险的产品体验机会

2. **灵活的授权管理**
   - 试用和正式授权状态分离
   - 支持针对不同状态进行统计和管理

3. **自动化试用处理**
   - 自动计算试用期限
   - 试用过期后自动提示用户购买正式授权

4. **防滥用机制**
   - 每个域名只能试用一次
   - 试用授权码内含试用标记，无法伪造

## 试用功能限制

1. **试用时长固定**
   - 所有域名试用期均为3天
   - 试用期不可延长

2. **试用后不可再次试用**
   - 同一域名试用过后不能重复试用
   - 防止用户无限试用逃避付费

3. **试用期间功能无限制**
   - 试用期间可使用全部功能，与正式授权相同
   - 仅时间上有限制

## 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|-----|------|---------|-------|
| v0.1 | [2024-08-19 12:15:00 +08:00] | 初始试用机制设计 | AR |