<?php
/**
 * 一花授权系统入口文件
 */
session_start();

// 加载配置和函数
require_once 'config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';

// 检查系统是否初始化
$db = DB::getInstance();
try {
    $db->query("SHOW TABLES LIKE '{$db->getPrefix()}domains'");
} catch (Exception $e) {
    // 系统未初始化，重定向到初始化页面
    header('Location: init.php');
    exit;
}

// 基础HTML头部
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><?php echo SITE_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo SITE_DESCRIPTION; ?>">
    <link rel="stylesheet" href="assets/layui/css/layui.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700&display=swap">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@2.5.0/fonts/remixicon.css">
    <script src="assets/layui/layui.js"></script>
    <style>
        :root {
            --primary-color: #3f51b5;
            --primary-light: #7986cb;
            --primary-dark: #303f9f;
            --accent-color: #ff4081;
            --text-primary: #333;
            --text-secondary: #666;
            --bg-color: #f9fafb;
            --card-bg: #ffffff;
            --success-color: #4caf50;
            --error-color: #f44336;
            --border-radius: 12px;
            --box-shadow: 0 10px 20px rgba(0,0,0,0.05);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Nunito', sans-serif;
            background: var(--bg-color);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            flex: 1;
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            position: relative;
        }
        
        .header:after {
            content: '';
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .header h1 {
            font-size: 2.8rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }
        
        .header p {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .domain-form {
            max-width: 600px;
            margin: 0 auto 40px;
            padding: 40px;
            background: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: all 0.3s ease;
        }
        
        .domain-form:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .layui-form-label {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .layui-input {
            height: 50px;
            line-height: 50px;
            padding: 0 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s;
        }
        
        .layui-input:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
        }
        
        .layui-btn {
            height: 50px;
            line-height: 50px;
            padding: 0 30px;
            background-color: var(--primary-color);
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s;
        }
        
        .layui-btn:hover {
            background-color: var(--primary-dark);
            box-shadow: 0 5px 15px rgba(63, 81, 181, 0.3);
            transform: translateY(-2px);
        }
        
        .layui-btn-normal {
            background-color: var(--accent-color);
        }
        
        .layui-btn-normal:hover {
            background-color: #e91e63;
            box-shadow: 0 5px 15px rgba(255, 64, 129, 0.3);
        }
        
        .result-box {
            max-width: 600px;
            margin: 0 auto 40px;
            padding: 40px;
            background: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            display: none;
            animation: fadeIn 0.5s ease;
            position: relative;
            overflow: hidden;
        }
        
        .result-box:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: var(--primary-color);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .result-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--primary-dark);
            display: flex;
            align-items: center;
        }
        
        .result-title i {
            margin-right: 10px;
            font-size: 1.6rem;
        }
        
        .result-item {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .result-label {
            width: 120px;
            font-weight: 600;
            color: var(--text-secondary);
        }
        
        .result-value {
            flex: 1;
            padding: 12px 20px;
            background: rgba(0,0,0,0.02);
            border-radius: 8px;
            font-weight: 600;
        }
        
        .success {
            color: var(--success-color);
        }
        
        .error {
            color: var(--error-color);
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 30px 0;
            color: var(--text-secondary);
            font-size: 14px;
            border-top: 1px solid rgba(0,0,0,0.05);
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.2rem;
            }
            
            .domain-form, .result-box {
                padding: 30px;
            }
            
            .result-item {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .result-label {
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo SITE_NAME; ?></h1>
            <p><?php echo SITE_DESCRIPTION; ?></p>
        </div>
        
        <div class="domain-form">
            <form class="layui-form" action="javascript:;" onsubmit="return false;">
                <div class="layui-form-item">
                    <label class="layui-form-label">域名</label>
                    <div class="layui-input-block">
                        <input type="text" name="domain" required lay-verify="required|domain" placeholder="请输入需要查询的域名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="domain-form"><i class="ri-search-line"></i> 查询授权状态</button>
                        <button class="layui-btn layui-btn-normal" id="download-btn" style="display:none;"><i class="ri-download-cloud-line"></i> 下载授权程序</button>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="result-box" id="result-box">
            <div class="result-title"><i class="ri-file-list-3-line"></i> 域名授权查询结果</div>
            
            <div class="result-item">
                <div class="result-label">授权状态</div>
                <div class="result-value" id="auth-status"></div>
            </div>
            
            <div class="result-item" id="expire-row" style="display:none;">
                <div class="result-label">过期时间</div>
                <div class="result-value" id="expire-time"></div>
            </div>
            
            <div class="result-item" id="remaining-row" style="display:none;">
                <div class="result-label">剩余天数</div>
                <div class="result-value" id="remaining-days"></div>
            </div>
            
            <div class="result-item" id="trial-row" style="display:none;">
                <div class="result-label">授权类型</div>
                <div class="result-value" id="trial-status"></div>
            </div>
        </div>
        
        <div class="footer">
            <p>Copyright &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?> All Rights Reserved.</p>
        </div>
    </div>
    
    <script>
    layui.use(['form', 'layer', 'jquery'], function(){
        var form = layui.form;
        var layer = layui.layer;
        var $ = layui.jquery;
        
        // 自定义验证规则
        form.verify({
            domain: [
                /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,
                '域名格式不正确'
            ]
        });
        
        // 监听提交
        form.on('submit(domain-form)', function(data){
            var domain = data.field.domain;
            
            // 显示加载层
            var loadIndex = layer.load(2);
            
            // 发送AJAX请求
            $.ajax({
                url: 'api/ajax.php?action=check_domain',
                type: 'POST',
                data: {domain: domain},
                dataType: 'json',
                success: function(res){
                    layer.close(loadIndex);
                    
                    // 显示结果区域
                    $('#result-box').hide().show(300);
                    
                    if(res.code === 0) {
                        // 授权有效
                        $('#auth-status').html('<i class="ri-checkbox-circle-line"></i> 授权有效').addClass('success').removeClass('error');
                        $('#expire-row').show();
                        $('#expire-time').text(res.data.expire_time);
                        $('#remaining-row').show();
                        $('#remaining-days').text(res.data.remaining_days + ' 天');
                        
                        // 显示试用状态
                        $('#trial-row').show();
                        if(res.data.is_trial) {
                            $('#trial-status').html('<i class="ri-time-line"></i> 试用版').addClass('error').removeClass('success');
                        } else {
                            $('#trial-status').html('<i class="ri-verified-badge-line"></i> 正式版').addClass('success').removeClass('error');
                        }
                        
                        // 显示下载按钮
                        $('#download-btn').fadeIn(300).unbind('click').click(function(){
                            downloadProgram(domain);
                        });
                    } else {
                        // 授权无效
                        $('#auth-status').html('<i class="ri-close-circle-line"></i> ' + res.msg).addClass('error').removeClass('success');
                        $('#expire-row').hide();
                        $('#remaining-row').hide();
                        $('#trial-row').hide();
                        $('#download-btn').hide();
                    }
                },
                error: function(){
                    layer.close(loadIndex);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
            
            return false;
        });
        
        // 下载程序
        function downloadProgram(domain) {
            var loadIndex = layer.load(2);
            
            $.ajax({
                url: 'api/ajax.php?action=download',
                type: 'POST',
                data: {domain: domain},
                dataType: 'json',
                success: function(res){
                    layer.close(loadIndex);
                    
                    if(res.code === 0) {
                        layer.msg('处理成功，即将开始下载', {icon: 1});
                        // 跳转到下载链接
                        window.location.href = res.data.download_url;
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                },
                error: function(){
                    layer.close(loadIndex);
                    layer.msg('网络错误，请稍后重试', {icon: 2});
                }
            });
        }
    });
    </script>
</body>
</html>