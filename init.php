<?php
/**
 * 系统初始化文件
 */
session_start();

// 加载配置
require_once 'config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';
require_once INCLUDE_PATH . 'auth.php';

// 初始化数据库
$db = DB::getInstance();
$db->buildTables();

// 创建必要目录
$dirs = [
    PROGRAM_PATH,
    TEMP_PATH,
    ROOT_PATH . '/logs'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 添加默认程序（如果不存在）
$program = $db->get('programs', ['id' => 1]);
if (!$program) {
    // 创建示例程序目录
    $programDir = PROGRAM_PATH . 'example_program';
    if (!is_dir($programDir)) {
        mkdir($programDir, 0755, true);
    }
    
    // 创建示例程序文件
    $includesDir = $programDir . '/includes';
    if (!is_dir($includesDir)) {
        mkdir($includesDir, 0755, true);
    }
    
    // 创建示例index.php
    $indexContent = '<?php
/**
 * 示例程序入口文件
 */
require_once \'includes/authcode.php\';

// 检查授权
if (!checkAuth()) {
    exit;
}

// 程序正常内容
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>授权系统示例程序</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #f9f9f9; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        h1 { color: #333; }
        .success { color: #4CAF50; }
        .info { margin-top: 20px; background: #e9f7ef; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>示例程序</h1>
        <p class="success">恭喜，您已成功授权！</p>
        
        <div class="info">
            <h3>授权信息</h3>
            <p>域名：<?php echo $_SERVER[\'HTTP_HOST\']; ?></p>
            <p>授权码：已验证</p>
            <p>状态：正常</p>
        </div>
    </div>
</body>
</html>';
    file_put_contents($programDir . '/index.php', $indexContent);
    
    // 创建示例authcode.php
    $authcodeContent = '<?php
// 授权码验证文件 - 示例程序
$authcode = "DEFAULT_PLACEHOLDER_CODE";

// 以下代码请勿修改
function checkAuth() {
    global $authcode;
    
    // 获取当前域名
    $domain = $_SERVER[\'HTTP_HOST\'];
    
    // 如果是本地测试环境，跳过验证
    if (in_array($domain, [\'localhost\', \'127.0.0.1\'])) {
        return true;
    }
    
    // 检查授权码是否为默认值
    if ($authcode == "DEFAULT_PLACEHOLDER_CODE") {
        showAuthError("程序未授权，请先获取授权");
        return false;
    }
    
    // 解析授权码
    $authInfo = decodeAuthCode($authcode);
    
    // 验证授权码与域名是否匹配
    if (!$authInfo || $authInfo[\'domain\'] != $domain) {
        showAuthError("授权码与当前域名不匹配");
        return false;
    }
    
    // 检查是否过期
    if (time() > $authInfo[\'expire_time\']) {
        showAuthTrialOption("授权已过期，请续费或试用");
        return false;
    }
    
    // 授权有效
    return true;
}

function decodeAuthCode($code) {
    // 简化的解析逻辑，实际应用中会更复杂
    $parts = explode(\'|\', base64_decode($code));
    if (count($parts) < 3) {
        return false;
    }
    
    return [
        \'domain\' => $parts[0],
        \'expire_time\' => intval($parts[1]),
        \'is_trial\' => ($parts[2] == \'1\')
    ];
}

function showAuthError($message) {
    echo \'<div style="padding:20px;background:#f8f8f8;border:1px solid #ddd;margin:20px;border-radius:5px;font-family:Arial;">\';
    echo \'<h2 style="color:#e74c3c;">\'.$message.\'</h2>\';
    echo \'<p>请联系管理员获取授权或<a href="http://localhost/auth/" target="_blank">点击此处</a>进入授权系统</p>\';
    echo \'</div>\';
    exit;
}

function showAuthTrialOption($message) {
    echo \'<div style="padding:20px;background:#f8f8f8;border:1px solid #ddd;margin:20px;border-radius:5px;font-family:Arial;">\';
    echo \'<h2 style="color:#e74c3c;">\'.$message.\'</h2>\';
    echo \'<p>您可以免费试用本程序3天。试用期结束后需要购买正式授权。</p>\';
    echo \'<button onclick="activateTrial()">立即试用</button>\';
    echo \'<script>
    function activateTrial() {
        var domain = window.location.hostname;
        var xhr = new XMLHttpRequest();
        xhr.open("POST", "http://localhost/auth/api/ajax.php?action=activate_trial", true);
        xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                var response = JSON.parse(xhr.responseText);
                if (response.code === 0) {
                    alert("试用激活成功，请刷新页面");
                    window.location.reload();
                } else {
                    alert("试用激活失败: " + response.msg);
                }
            }
        };
        xhr.send("domain=" + domain);
    }
    </script>\';
    echo \'</div>\';
    exit;
}
?>';
    file_put_contents($includesDir . '/authcode.php', $authcodeContent);
    
    // 添加到数据库
    $db->insert('programs', [
        'name' => '示例程序',
        'version' => '1.0',
        'path' => 'example_program',
        'description' => '用于测试授权系统的示例程序',
        'create_time' => date('Y-m-d H:i:s'),
        'update_time' => date('Y-m-d H:i:s')
    ]);
}

echo '系统初始化完成';