<?php
/**
 * 系统设置页面
 */
// 包含头部
require_once 'common/header.php';

// 生成CSRF令牌
$csrfToken = generateCsrfToken();

// 获取系统配置
$db = DB::getInstance();
$settings = [
    'site_name' => SITE_NAME,
    'site_description' => SITE_DESCRIPTION,
    'trial_days' => TRIAL_DAYS
];
?>

<!-- 设置表单 -->
<div class="layui-tab layui-tab-brief">
    <ul class="layui-tab-title">
        <li class="layui-this">基本设置</li>
        <li>管理员密码</li>
        <li>系统维护</li>
    </ul>
    <div class="layui-tab-content">
        <!-- 基本设置 -->
        <div class="layui-tab-item layui-show">
            <form class="layui-form" lay-filter="basic-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <input type="hidden" name="type" value="basic">
                
                <div class="layui-form-item">
                    <label class="layui-form-label">网站名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="site_name" value="<?php echo $settings['site_name']; ?>" required lay-verify="required" placeholder="请输入网站名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">网站描述</label>
                    <div class="layui-input-block">
                        <input type="text" name="site_description" value="<?php echo $settings['site_description']; ?>" required lay-verify="required" placeholder="请输入网站描述" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">试用天数</label>
                    <div class="layui-input-inline">
                        <input type="number" name="trial_days" value="<?php echo $settings['trial_days']; ?>" required lay-verify="required|number" placeholder="请输入试用天数" autocomplete="off" class="layui-input">
                    </div>
                    <div class="layui-form-mid layui-word-aux">天</div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="basic-submit">保存设置</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 管理员密码 -->
        <div class="layui-tab-item">
            <form class="layui-form" lay-filter="password-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <input type="hidden" name="type" value="password">
                
                <div class="layui-form-item">
                    <label class="layui-form-label">原密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="old_password" required lay-verify="required" placeholder="请输入原密码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">新密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="new_password" required lay-verify="required|password" placeholder="请输入新密码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">确认密码</label>
                    <div class="layui-input-block">
                        <input type="password" name="confirm_password" required lay-verify="required|confirmPassword" placeholder="请再次输入新密码" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="password-submit">修改密码</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 系统维护 -->
        <div class="layui-tab-item">
            <div class="layui-card">
                <div class="layui-card-header">清理临时文件</div>
                <div class="layui-card-body">
                    <p>清理系统生成的临时文件，包括下载临时文件、缓存等。</p>
                    <button class="layui-btn" id="clear-temp-btn">立即清理</button>
                </div>
            </div>
            
            <div class="layui-card layui-margin-top">
                <div class="layui-card-header">数据库备份</div>
                <div class="layui-card-body">
                    <p>备份系统数据库，保存重要数据。</p>
                    <button class="layui-btn" id="backup-db-btn">立即备份</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
layui.use(['form', 'layer', 'element'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var element = layui.element;
    var $ = layui.jquery;
    
    // 自定义验证规则
    form.verify({
        password: [
            /^[\S]{6,16}$/,
            '密码必须6到16位，且不能出现空格'
        ],
        confirmPassword: function(value){
            var password = $('input[name=new_password]').val();
            if(value !== password){
                return '两次输入的密码不一致';
            }
        }
    });
    
    // 监听基本设置表单提交
    form.on('submit(basic-submit)', function(data){
        var field = data.field;
        
        // 发送请求
        $.ajax({
            url: 'ajax.php?action=update_settings',
            type: 'POST',
            data: field,
            dataType: 'json',
            success: function(res){
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
        
        return false;
    });
    
    // 监听密码修改表单提交
    form.on('submit(password-submit)', function(data){
        var field = data.field;
        
        // 发送请求
        $.ajax({
            url: 'ajax.php?action=update_password',
            type: 'POST',
            data: field,
            dataType: 'json',
            success: function(res){
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                    // 重置表单
                    $('form[lay-filter=password-form]')[0].reset();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
        
        return false;
    });
    
    // 清理临时文件按钮点击事件
    $('#clear-temp-btn').on('click', function(){
        var loadIndex = layer.load(2);
        
        $.ajax({
            url: 'ajax.php?action=clear_temp',
            type: 'POST',
            data: {
                csrf_token: '<?php echo $csrfToken; ?>'
            },
            dataType: 'json',
            success: function(res){
                layer.close(loadIndex);
                
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
    });
    
    // 数据库备份按钮点击事件
    $('#backup-db-btn').on('click', function(){
        var loadIndex = layer.load(2);
        
        $.ajax({
            url: 'ajax.php?action=backup_db',
            type: 'POST',
            data: {
                csrf_token: '<?php echo $csrfToken; ?>'
            },
            dataType: 'json',
            success: function(res){
                layer.close(loadIndex);
                
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                    // 如果有下载链接，就下载
                    if(res.data && res.data.download_url){
                        window.location.href = res.data.download_url;
                    }
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
    });
});
</script>

<style>
.layui-margin-top {
    margin-top: 15px;
}
</style>

<?php
// 包含底部
require_once 'common/footer.php';
?>