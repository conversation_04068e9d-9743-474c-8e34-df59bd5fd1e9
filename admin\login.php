<?php
/**
 * 管理员登录页面
 */
session_start();

// 如果已经登录，则跳转到后台首页
if (isset($_SESSION['admin_id'])) {
    header('Location: index.php');
    exit;
}

// 加载必要文件
require_once '../config.php';
require_once INCLUDE_PATH . 'functions.php';
require_once INCLUDE_PATH . 'db.php';

// 处理登录请求
$loginError = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['username'], $_POST['password'])) {
    $username = cleanInput($_POST['username']);
    $password = $_POST['password'];
    
    // 验证用户名和密码
    $db = DB::getInstance();
    $admin = $db->get('admins', ['username' => $username]);
    
    if ($admin && password_verify($password, $admin['password'])) {
        // 登录成功，设置session
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_login_time'] = time();
        
        // 更新最后登录时间和IP
        $db->update('admins', [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => getClientIp()
        ], ['id' => $admin['id']]);
        
        // 跳转到后台首页
        header('Location: index.php');
        exit;
    } else {
        $loginError = '用户名或密码错误';
    }
}

// 生成CSRF令牌
$csrfToken = generateCsrfToken();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>管理员登录 - <?php echo SITE_NAME; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../assets/layui/css/layui.css">
    <script src="../assets/layui/layui.js"></script>
    <style>
        body {
            background-color: #f2f2f2;
            padding: 0;
            margin: 0;
        }
        .login-container {
            width: 350px;
            margin: 100px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 4px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .login-title {
            text-align: center;
            margin-bottom: 20px;
        }
        .login-form {
            margin-top: 20px;
        }
        .layui-form-item {
            margin-bottom: 20px;
        }
        .login-btn {
            width: 100%;
        }
        .login-error {
            color: #FF5722;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-title">
            <h2><?php echo SITE_NAME; ?></h2>
            <p>管理员登录</p>
        </div>
        
        <?php if ($loginError): ?>
        <div class="login-error">
            <i class="layui-icon layui-icon-close-fill"></i> <?php echo $loginError; ?>
        </div>
        <?php endif; ?>
        
        <form class="layui-form login-form" method="post" action="">
            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
            
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 0;">
                    <input type="text" name="username" required lay-verify="required" placeholder="用户名" autocomplete="off" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 0;">
                    <input type="password" name="password" required lay-verify="required" placeholder="密码" autocomplete="off" class="layui-input">
                </div>
            </div>
            
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 0;">
                    <button class="layui-btn login-btn" lay-submit lay-filter="login-form">登录</button>
                </div>
            </div>
        </form>
    </div>
    
    <script>
    layui.use(['form', 'layer'], function(){
        var form = layui.form;
        var layer = layui.layer;
        
        // 表单验证
        form.verify({
            username: function(value){
                if(value.length < 3){
                    return '用户名至少3个字符';
                }
            },
            password: function(value){
                if(value.length < 6){
                    return '密码至少6个字符';
                }
            }
        });
        
        // 监听提交
        form.on('submit(login-form)', function(data){
            return true;
        });
    });
    </script>
</body>
</html>