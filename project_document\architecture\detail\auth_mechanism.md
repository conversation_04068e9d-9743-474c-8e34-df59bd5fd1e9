# 域名授权系统授权机制设计

创建时间: [2024-08-19 11:00:00 +08:00]
创建者: 系统架构师(AR)
最后更新: [2024-08-19 11:00:00 +08:00]

## 授权码生成机制

### 授权码组成
授权码采用多因素混合设计，主要包含以下因素：

1. **域名信息**: 使用域名的特征值（如哈希值）作为因子
2. **时间信息**: 包含创建时间和过期时间的加密信息
3. **唯一标识**: 随机生成的唯一标识符
4. **校验信息**: 用于验证授权码完整性的校验值

### 授权码生成算法
```php
/**
 * 生成授权码
 * @param string $domain 域名
 * @param string $expireTime 过期时间 (Y-m-d H:i:s 格式)
 * @return string 生成的授权码
 */
function generateAuthCode($domain, $expireTime) {
    // 1. 域名处理
    $domainHash = md5($domain);
    
    // 2. 时间信息处理
    $createTime = date('YmdHis');
    $expireTimestamp = strtotime($expireTime);
    $expireCode = base64_encode($expireTimestamp);
    
    // 3. 生成随机唯一标识 (16字符)
    $uniqueId = bin2hex(random_bytes(8));
    
    // 4. 组合基础授权码
    $baseCode = $domainHash . '_' . $createTime . '_' . $expireCode . '_' . $uniqueId;
    
    // 5. 生成校验信息
    $checksum = md5($baseCode . 'AUTH_SALT_KEY');
    $checksumShort = substr($checksum, 0, 8);
    
    // 6. 最终授权码
    $authCode = $baseCode . '_' . $checksumShort;
    
    // 7. 加密处理
    $encryptedCode = customEncrypt($authCode);
    
    return $encryptedCode;
}

/**
 * 自定义加密函数
 * @param string $data 要加密的数据
 * @return string 加密后的数据
 */
function customEncrypt($data) {
    // 实际实现中会使用更复杂的加密算法
    // 这里仅作示例
    $key = 'YOUR_SECRET_KEY';
    $method = 'aes-256-cbc';
    $iv = substr(hash('sha256', $key), 0, 16);
    
    $encrypted = openssl_encrypt($data, $method, $key, 0, $iv);
    return $encrypted;
}
```

### 授权码验证流程

1. **本地验证** (程序内置验证)
   ```php
   function verifyAuthCodeLocally($authCode, $domain) {
       // 1. 解密授权码
       $decryptedCode = customDecrypt($authCode);
       
       // 2. 分解授权码
       list($domainHash, $createTime, $expireCode, $uniqueId, $checksumShort) = explode('_', $decryptedCode);
       
       // 3. 验证域名
       if (md5($domain) !== $domainHash) {
           return false; // 域名不匹配
       }
       
       // 4. 验证校验和
       $baseCode = $domainHash . '_' . $createTime . '_' . $expireCode . '_' . $uniqueId;
       $expectedChecksum = substr(md5($baseCode . 'AUTH_SALT_KEY'), 0, 8);
       if ($checksumShort !== $expectedChecksum) {
           return false; // 校验和不匹配，授权码可能被篡改
       }
       
       // 5. 验证过期时间
       $expireTimestamp = base64_decode($expireCode);
       if (time() > $expireTimestamp) {
           return false; // 授权已过期
       }
       
       return true; // 授权有效
   }
   ```

2. **远程验证** (联网时的二次验证)
   ```php
   function verifyAuthCodeRemotely($authCode, $domain) {
       // 构造请求参数
       $params = [
           'authCode' => $authCode,
           'domain' => $domain,
           'timestamp' => time(),
           // 其他可能的参数...
       ];
       
       // 添加签名
       $params['sign'] = generateSign($params);
       
       // 发送请求到验证服务器
       $result = sendRequest('https://auth.example.com/api/verify', $params);
       
       return $result['valid'] ?? false;
   }
   ```

## 授权程序处理流程

### 1. 程序下载和授权码嵌入流程

```
用户请求 → 验证域名授权 → 复制程序到临时目录 → 生成授权码 →
修改authcode.php → 打包程序 → 提供下载链接 → 记录下载信息 → 清理临时文件
```

### 2. 程序源文件格式要求

程序源文件必须包含 `/includes/authcode.php` 文件，文件内容格式如下：

```php
<?php
// 授权码配置
$authcode = "DEFAULT_PLACEHOLDER_CODE"; // 此处将被替换为实际授权码

// 以下为授权验证逻辑，不应被修改
function checkAuth() {
    global $authcode;
    // 授权验证逻辑...
}
?>
```

### 3. 授权码写入实现

```php
/**
 * 将授权码写入程序文件
 * @param string $programPath 程序路径
 * @param string $authCode 授权码
 * @return bool 是否成功
 */
function writeAuthCodeToProgram($programPath, $authCode) {
    $authFilePath = $programPath . '/includes/authcode.php';
    
    // 检查文件是否存在
    if (!file_exists($authFilePath)) {
        return false;
    }
    
    // 读取文件内容
    $content = file_get_contents($authFilePath);
    
    // 替换授权码
    $pattern = '/\$authcode\s*=\s*"[^"]*"\s*;/';
    $replacement = '$authcode = "' . $authCode . '";';
    $newContent = preg_replace($pattern, $replacement, $content);
    
    // 写入文件
    $result = file_put_contents($authFilePath, $newContent);
    
    return ($result !== false);
}
```

## 安全性考虑

1. **授权码防篡改**：
   - 使用校验和验证授权码完整性
   - 混合多种因素增加破解难度

2. **授权码防复制**：
   - 授权码与域名绑定
   - 可选的硬件指纹绑定（如服务器MAC地址等）

3. **防止逆向工程**：
   - 使用混淆技术处理授权码生成和验证逻辑
   - 考虑使用PHP扩展或编译后的二进制模块处理核心验证逻辑

4. **持续验证**：
   - 授权程序可定期向授权中心验证授权状态
   - 支持远程禁用已授权但违规的程序

## 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|-----|------|---------|-------|
| v0.1 | [2024-08-19 11:00:00 +08:00] | 初始授权机制设计 | AR |