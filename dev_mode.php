<?php
/**
 * 开发模式配置文件
 * 此文件仅用于本地开发环境，请勿部署到生产环境
 */

return [
    // 是否启用开发模式（true启用，false禁用）
    'enabled' => false,
    
    // 开发模式安全密钥，必须与auth_verify.php中的设置匹配
    'dev_key' => 'SECURE_DEV_KEY_HERE',
    
    // 开发环境相关设置
    'settings' => [
        // 显示详细错误信息
        'display_errors' => true,
        
        // 日志详细级别（0-关闭，1-错误，2-警告，3-信息，4-调试）
        'log_level' => 4,
        
        // 跳过的安全检查（可选）
        'skip_security_checks' => [
            'domain_verification' => true,   // 跳过域名验证
            'expiration_check' => true,      // 跳过过期检查
            'debug_detection' => true,       // 跳过调试环境检测
        ],
        
        // 开发账号（可用于后台管理，避免使用生产环境账号）
        'admin_account' => [
            'username' => 'dev_admin',
            'password' => 'dev_password'     // 仅用于开发，生产环境应使用更强的密码
        ],
    ],
    
    // 开发者信息
    'developer' => [
        'name' => '开发者姓名',
        'email' => '<EMAIL>'
    ],
    
    // 当前版本号（用于测试不同版本功能）
    'version' => '1.0.0-dev',
]; 