<?php
/**
 * 定时任务脚本
 * 用于清理临时文件
 * 
 * 可以通过cron定时执行: * * * * * php /path/to/auth/cron.php
 */

// 加载配置
require_once 'config.php';

// 清理临时下载文件
$cleanupFile = TEMP_PATH . 'cleanup.txt';
if (file_exists($cleanupFile)) {
    $lines = file($cleanupFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $newLines = [];
    
    foreach ($lines as $line) {
        list($filePath, $cleanupTime) = explode('|', $line);
        
        // 检查是否到了清理时间
        if (time() >= $cleanupTime) {
            // 如果文件存在，删除它
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        } else {
            // 时间未到，保留该行
            $newLines[] = $line;
        }
    }
    
    // 更新清理文件
    file_put_contents($cleanupFile, implode(PHP_EOL, $newLines));
}

// 清理超过24小时的临时目录
$tempDirs = glob(TEMP_PATH . 'download_*');
foreach ($tempDirs as $dir) {
    // 获取目录修改时间
    $modTime = filemtime($dir);
    
    // 如果目录修改时间超过24小时，删除它
    if (time() - $modTime >= 86400) {
        // 递归删除目录
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), ['.', '..']);
            foreach ($files as $file) {
                $path = $dir . '/' . $file;
                is_dir($path) ? rmdirRecursive($path) : unlink($path);
            }
            rmdir($dir);
        }
    }
}

/**
 * 递归删除目录
 * 
 * @param string $dir 目录路径
 */
function rmdirRecursive($dir) {
    if (!is_dir($dir)) {
        return;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        is_dir($path) ? rmdirRecursive($path) : unlink($path);
    }
    
    rmdir($dir);
}

echo "清理任务完成，时间: " . date('Y-m-d H:i:s');