<?php
/**
 * 下载数据库备份文件
 */
session_start();

// 检查是否登录
if (!isset($_SESSION['admin_id'])) {
    header('HTTP/1.1 403 Forbidden');
    exit('未授权访问');
}

// 检查下载令牌
$token = isset($_GET['token']) ? $_GET['token'] : '';
if (empty($token) || !isset($_SESSION['backup_token']) || $token !== $_SESSION['backup_token']) {
    header('HTTP/1.1 403 Forbidden');
    exit('无效的下载令牌');
}

// 检查令牌是否过期
if (!isset($_SESSION['backup_expire']) || $_SESSION['backup_expire'] < time()) {
    header('HTTP/1.1 403 Forbidden');
    exit('下载令牌已过期');
}

// 检查文件是否存在
$backupFile = isset($_SESSION['backup_file']) ? $_SESSION['backup_file'] : '';
$backupName = isset($_SESSION['backup_name']) ? $_SESSION['backup_name'] : 'backup.sql';

if (empty($backupFile) || !file_exists($backupFile)) {
    header('HTTP/1.1 404 Not Found');
    exit('备份文件不存在');
}

// 下载文件
header('Content-Description: File Transfer');
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename=' . $backupName);
header('Expires: 0');
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Content-Length: ' . filesize($backupFile));
readfile($backupFile);

// 清除会话数据
unset($_SESSION['backup_token']);
unset($_SESSION['backup_file']);
unset($_SESSION['backup_name']);
unset($_SESSION['backup_expire']);

exit;