<?php
/**
 * UI美化演示页面
 * 展示所有美化后的用户界面
 */

// 引入授权验证文件
require_once __DIR__ . '/includes/auth_verify.php';

// 获取演示类型
$demo = $_GET['demo'] ?? 'menu';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权系统UI演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .demo-title {
            text-align: center;
            color: #2c3e50;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 40px;
        }
        .demo-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .demo-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .demo-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }
        .demo-item h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .demo-item p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }
        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .back-link:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <?php if ($demo === 'menu'): ?>
            <h1 class="demo-title">🎨 授权系统UI演示</h1>
            <div class="demo-menu">
                <a href="?demo=error" class="demo-item">
                    <h3>⚠️ 错误提示界面</h3>
                    <p>展示授权错误时的用户界面</p>
                </a>
                <a href="?demo=renewal" class="demo-item">
                    <h3>⏰ 续费提示界面</h3>
                    <p>展示授权过期时的续费引导</p>
                </a>
                <a href="?demo=trial" class="demo-item">
                    <h3>🚀 试用激活界面</h3>
                    <p>展示免费试用的激活页面</p>
                </a>
                <a href="?demo=emergency" class="demo-item">
                    <h3>⚡ 紧急授权提示</h3>
                    <p>展示紧急模式的通知样式</p>
                </a>
                <a href="?demo=dev" class="demo-item">
                    <h3>🔧 开发模式提示</h3>
                    <p>展示开发模式的状态指示</p>
                </a>
            </div>
            <div style="text-align: center; color: #7f8c8d;">
                <p>点击上方选项查看对应的UI界面演示</p>
                <p><small>所有界面都已经过现代化美化处理</small></p>
            </div>
        <?php else: ?>
            <a href="?demo=menu" class="back-link">← 返回演示菜单</a>
            
            <?php
            // 根据演示类型显示对应界面
            switch ($demo) {
                case 'error':
                    showAuthError("授权验证失败");
                    break;
                    
                case 'renewal':
                    showAuthRenewal("授权已过期");
                    break;
                    
                case 'trial':
                    showAuthTrialOption("程序未授权，您可以先试用");
                    break;
                    
                case 'emergency':
                    echo '<div style="padding: 20px; text-align: center;">';
                    echo '<h2>紧急授权提示演示</h2>';
                    echo '<p>紧急授权提示会显示在页面右上角</p>';
                    showEmergencyNotice();
                    echo '</div>';
                    break;
                    
                case 'dev':
                    echo '<div style="padding: 20px; text-align: center;">';
                    echo '<h2>开发模式提示演示</h2>';
                    echo '<p>开发模式提示会显示在页面左下角</p>';
                    echo '<div style="
                        position: fixed;
                        bottom: 20px;
                        left: 20px;
                        z-index: 9999;
                        background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
                        border: none;
                        border-radius: 12px;
                        padding: 12px 16px;
                        box-shadow: 0 8px 32px rgba(127, 205, 205, 0.3);
                        font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;
                        animation: slideInLeft 0.5s ease-out;
                        backdrop-filter: blur(10px);
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            color: #2d3436;
                            font-size: 13px;
                            font-weight: 500;
                        ">
                            <span style="
                                font-size: 16px;
                                margin-right: 8px;
                                animation: bounce 1s infinite;
                            ">🔧</span>
                            <span style="font-weight: 600;">开发者模式</span>
                            <span style="margin-left: 8px; opacity: 0.7;">授权验证已跳过</span>
                        </div>
                        <style>
                            @keyframes slideInLeft {
                                from { transform: translateX(-100%); opacity: 0; }
                                to { transform: translateX(0); opacity: 1; }
                            }
                            @keyframes bounce {
                                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                                40% { transform: translateY(-4px); }
                                60% { transform: translateY(-2px); }
                            }
                        </style>
                    </div>';
                    echo '</div>';
                    break;
                    
                default:
                    echo '<h2>未知的演示类型</h2>';
                    break;
            }
            ?>
        <?php endif; ?>
    </div>
</body>
</html>
