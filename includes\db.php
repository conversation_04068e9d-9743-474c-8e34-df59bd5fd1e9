<?php
/**
 * 数据库操作类
 */
class DB {
    private static $instance;
    private $conn;
    private $prefix;
    
    /**
     * 构造函数，连接数据库
     */
    private function __construct() {
        $this->prefix = DB_PREFIX;
        try {
            $this->conn = new PDO(
                'mysql:host=' . DB_HOST . ';port=' . DB_PORT . ';dbname=' . DB_NAME . ';charset=utf8mb4',
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            die('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取数据库实例（单例模式）
     * 
     * @return DB 数据库实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 执行SQL查询
     * 
     * @param string $sql SQL语句
     * @param array $params 参数数组
     * @return PDOStatement 查询结果
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            if (DEBUG) {
                die('SQL执行错误: ' . $e->getMessage() . '<br>SQL: ' . $sql);
            } else {
                error_log('SQL执行错误: ' . $e->getMessage() . ' SQL: ' . $sql);
                die('数据库操作失败');
            }
        }
    }
    
    /**
     * 获取单条记录
     * 
     * @param string $table 表名
     * @param string|array $where 条件
     * @param string $fields 字段
     * @return array|false 记录数组或false
     */
    public function get($table, $where = '', $fields = '*') {
        $table = $this->prefix . $table;
        $sql = "SELECT {$fields} FROM {$table}";
        
        $params = [];
        if (is_array($where)) {
            $conditions = [];
            foreach ($where as $key => $value) {
                $conditions[] = "`{$key}` = ?";
                $params[] = $value;
            }
            if (!empty($conditions)) {
                $sql .= " WHERE " . implode(' AND ', $conditions);
            }
        } elseif (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 获取多条记录
     * 
     * @param string $table 表名
     * @param string|array $where 条件
     * @param string $fields 字段
     * @param string $orderby 排序
     * @param string $limit 限制
     * @return array 记录数组
     */
    public function getAll($table, $where = '', $fields = '*', $orderby = '', $limit = '') {
        $table = $this->prefix . $table;
        $sql = "SELECT {$fields} FROM {$table}";
        
        $params = [];
        if (is_array($where)) {
            $conditions = [];
            foreach ($where as $key => $value) {
                $conditions[] = "`{$key}` = ?";
                $params[] = $value;
            }
            if (!empty($conditions)) {
                $sql .= " WHERE " . implode(' AND ', $conditions);
            }
        } elseif (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        if (!empty($orderby)) {
            $sql .= " ORDER BY {$orderby}";
        }
        
        if (!empty($limit)) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 插入记录
     * 
     * @param string $table 表名
     * @param array $data 数据
     * @return int 影响行数
     */
    public function insert($table, $data) {
        $table = $this->prefix . $table;
        $fields = array_keys($data);
        $fieldsStr = '`' . implode('`, `', $fields) . '`';
        $placeholders = rtrim(str_repeat('?, ', count($fields)), ', ');
        
        $sql = "INSERT INTO {$table} ({$fieldsStr}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, array_values($data));
        
        return $stmt->rowCount();
    }
    
    /**
     * 更新记录
     * 
     * @param string $table 表名
     * @param array $data 数据
     * @param string|array $where 条件
     * @return int 影响行数
     */
    public function update($table, $data, $where) {
        $table = $this->prefix . $table;
        $fields = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            $fields[] = "`{$key}` = ?";
            $params[] = $value;
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $fields);
        
        if (is_array($where)) {
            $conditions = [];
            foreach ($where as $key => $value) {
                $conditions[] = "`{$key}` = ?";
                $params[] = $value;
            }
            if (!empty($conditions)) {
                $sql .= " WHERE " . implode(' AND ', $conditions);
            }
        } elseif (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 删除记录
     * 
     * @param string $table 表名
     * @param string|array $where 条件
     * @return int 影响行数
     */
    public function delete($table, $where) {
        $table = $this->prefix . $table;
        $sql = "DELETE FROM {$table}";
        
        $params = [];
        if (is_array($where)) {
            $conditions = [];
            foreach ($where as $key => $value) {
                $conditions[] = "`{$key}` = ?";
                $params[] = $value;
            }
            if (!empty($conditions)) {
                $sql .= " WHERE " . implode(' AND ', $conditions);
            }
        } elseif (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 获取最后插入的ID
     * 
     * @return string 最后插入的ID
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->conn->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollBack() {
        return $this->conn->rollBack();
    }
    
    /**
     * 获取表前缀
     * 
     * @return string 表前缀
     */
    public function getPrefix() {
        return $this->prefix;
    }
    
    /**
     * 构建数据表
     * 创建系统所需的数据表
     */
    public function buildTables() {
        // 域名表
        $sql = "CREATE TABLE IF NOT EXISTS `{$this->prefix}domains` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `domain` varchar(255) NOT NULL COMMENT '域名',
            `auth_code` varchar(255) NOT NULL COMMENT '授权码',
            `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
            `is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否试用状态:0否,1是',
            `trial_start_time` datetime DEFAULT NULL COMMENT '试用开始时间',
            `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
            `create_time` datetime NOT NULL COMMENT '创建时间',
            `update_time` datetime NOT NULL COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `domain` (`domain`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        $this->query($sql);
        
        // 下载记录表
        $sql = "CREATE TABLE IF NOT EXISTS `{$this->prefix}downloads` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `domain` varchar(255) NOT NULL COMMENT '域名',
            `auth_code` varchar(255) NOT NULL COMMENT '授权码',
            `download_time` datetime NOT NULL COMMENT '下载时间',
            `ip` varchar(50) NOT NULL COMMENT 'IP地址',
            `program_id` int(11) NOT NULL COMMENT '程序ID',
            `is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否试用下载:0否,1是',
            PRIMARY KEY (`id`),
            KEY `idx_domain` (`domain`),
            KEY `idx_download_time` (`download_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        $this->query($sql);
        
        // 程序表
        $sql = "CREATE TABLE IF NOT EXISTS `{$this->prefix}programs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL COMMENT '程序名称',
            `version` varchar(50) NOT NULL COMMENT '程序版本',
            `path` varchar(255) NOT NULL COMMENT '程序存放路径',
            `description` text COMMENT '程序描述',
            `create_time` datetime NOT NULL COMMENT '创建时间',
            `update_time` datetime NOT NULL COMMENT '更新时间',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        $this->query($sql);
        
        // 管理员表
        $sql = "CREATE TABLE IF NOT EXISTS `{$this->prefix}admins` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL COMMENT '用户名',
            `password` varchar(255) NOT NULL COMMENT '密码',
            `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
            `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
            `create_time` datetime NOT NULL COMMENT '创建时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        $this->query($sql);
        
        // 操作日志表
        $sql = "CREATE TABLE IF NOT EXISTS `{$this->prefix}admin_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `admin_id` int(11) NOT NULL COMMENT '管理员ID',
            `admin_username` varchar(50) NOT NULL COMMENT '管理员用户名',
            `action` varchar(50) NOT NULL COMMENT '操作类型',
            `description` text NOT NULL COMMENT '操作描述',
            `target_id` int(11) DEFAULT 0 COMMENT '操作目标ID',
            `target_type` varchar(50) DEFAULT '' COMMENT '操作目标类型',
            `ip` varchar(50) NOT NULL COMMENT 'IP地址',
            `create_time` datetime NOT NULL COMMENT '创建时间',
            PRIMARY KEY (`id`),
            KEY `admin_id` (`admin_id`),
            KEY `action` (`action`),
            KEY `target_id` (`target_id`),
            KEY `target_type` (`target_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        $this->query($sql);
        
        // 初始化管理员账号
        $admin = $this->get('admins', ['username' => 'admin']);
        if (!$admin) {
            $this->insert('admins', [
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'create_time' => date('Y-m-d H:i:s')
            ]);
        }
    }
}