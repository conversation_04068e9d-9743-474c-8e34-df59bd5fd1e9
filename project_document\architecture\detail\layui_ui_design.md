# 域名授权系统UI设计 (Lay<PERSON>实现)

创建时间: [2024-08-19 11:20:00 +08:00]
创建者: UI/UX设计师
最后更新: [2024-08-19 11:20:00 +08:00]

## 整体设计风格

系统UI采用Layui框架实现，整体风格简洁、直观、专业。遵循以下设计原则：

1. **简洁明了**: 界面简洁，突出核心功能
2. **引导性强**: 用户可直观理解操作流程
3. **一致性**: 保持界面元素的一致性
4. **响应式**: 兼容不同设备和屏幕尺寸

## 页面结构

### 1. 布局结构

系统采用Layui的经典布局结构：

```
+----------------------------------------+
|              顶部导航栏                |
+----------------------------------------+
|        |                              |
|        |                              |
| 左侧   |         主内容区域            |
| 菜单   |                              |
|        |                              |
|        |                              |
+----------------------------------------+
|              底部版权栏                |
+----------------------------------------+
```

### 2. 颜色方案

主要颜色：
- 主色调: #009688 (Layui默认绿色)
- 辅助色: #1E9FFF (蓝色)
- 警告色: #FFB800 (黄色)
- 危险色: #FF5722 (红色)
- 背景色: #F2F2F2 (浅灰色)

## 主要页面设计

### 1. 前台首页

![首页布局草图]

```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>域名授权系统</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="./assets/layui/css/layui.css">
</head>
<body>
  <!-- 顶部导航 -->
  <div class="layui-header">
    <div class="layui-container">
      <div class="layui-logo">域名授权系统</div>
    </div>
  </div>
  
  <!-- 主体内容 -->
  <div class="layui-container" style="margin-top: 30px;">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">域名查询</div>
          <div class="layui-card-body">
            <div class="layui-form">
              <div class="layui-form-item">
                <div class="layui-input-group">
                  <div class="layui-input-split layui-input-prefix">
                    域名
                  </div>
                  <input type="text" name="domain" placeholder="请输入域名(例如: example.com)" class="layui-input">
                  <div class="layui-input-suffix">
                    <button type="button" class="layui-btn" id="checkDomain">查询</button>
                  </div>
                </div>
              </div>
            </div>
            <div id="resultArea" style="display:none;">
              <div class="layui-collapse">
                <div class="layui-colla-item">
                  <h2 class="layui-colla-title">查询结果</h2>
                  <div class="layui-colla-content layui-show" id="resultContent">
                    <!-- 查询结果将在这里显示 -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="layui-row layui-col-space15" style="margin-top: 20px;">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">程序下载</div>
          <div class="layui-card-body">
            <div class="layui-form" id="downloadForm" style="display:none;">
              <!-- 程序下载表单将在域名授权验证通过后显示 -->
            </div>
            <div id="downloadMessage">
              请先进行域名授权查询
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 底部 -->
  <div class="layui-footer" style="text-align: center; margin-top: 30px;">
    © 2024 域名授权系统
  </div>
  
  <script src="./assets/layui/layui.js"></script>
  <script>
  layui.use(['layer', 'form', 'element'], function(){
    var layer = layui.layer;
    var form = layui.form;
    var element = layui.element;
    var $ = layui.$;
    
    // 域名查询
    $('#checkDomain').on('click', function(){
      var domain = $('input[name="domain"]').val();
      if(!domain){
        layer.msg('请输入域名');
        return;
      }
      
      // 显示加载中
      var loadIndex = layer.load(2);
      
      // 发送AJAX请求
      $.ajax({
        url: 'api/ajax.php?action=check_domain',
        type: 'POST',
        data: {domain: domain},
        dataType: 'json',
        success: function(res){
          layer.close(loadIndex);
          
          // 显示结果区域
          $('#resultArea').show();
          
          if(res.code === 0){
            // 成功
            var html = '';
            if(res.data.is_auth){
              html = '<div class="layui-bg-green" style="padding: 10px;">域名 <b>' + domain + '</b> 已授权</div>';
              html += '<div style="margin-top: 10px;">授权到期时间: ' + res.data.expire_time + '</div>';
              
              // 显示下载表单
              $('#downloadMessage').hide();
              $('#downloadForm').show();
              $('#downloadForm').html(
                '<div class="layui-form-item">' +
                  '<label class="layui-form-label">选择程序</label>' +
                  '<div class="layui-input-block">' +
                    '<select name="program_id" lay-filter="program">' +
                      '<option value="">请选择要下载的程序</option>' +
                      '<!-- 这里将通过AJAX加载可用程序列表 -->' +
                    '</select>' +
                  '</div>' +
                '</div>' +
                '<div class="layui-form-item">' +
                  '<div class="layui-input-block">' +
                    '<button type="button" class="layui-btn" id="downloadBtn" disabled>下载</button>' +
                  '</div>' +
                '</div>'
              );
              
              // 加载程序列表
              loadPrograms(domain);
              
              // 重新渲染表单
              form.render();
            } else {
              html = '<div class="layui-bg-red" style="padding: 10px;">域名 <b>' + domain + '</b> 未授权</div>';
              
              // 隐藏下载表单
              $('#downloadForm').hide();
              $('#downloadMessage').show();
              $('#downloadMessage').html('该域名未授权，无法下载程序');
            }
            
            $('#resultContent').html(html);
          } else {
            // 失败
            $('#resultContent').html('<div class="layui-bg-red" style="padding: 10px;">查询失败: ' + res.msg + '</div>');
            
            // 隐藏下载表单
            $('#downloadForm').hide();
            $('#downloadMessage').show();
            $('#downloadMessage').html('查询失败，请重试');
          }
        },
        error: function(){
          layer.close(loadIndex);
          $('#resultContent').html('<div class="layui-bg-red" style="padding: 10px;">网络错误，请重试</div>');
        }
      });
    });
    
    // 加载程序列表
    function loadPrograms(domain){
      $.ajax({
        url: 'api/ajax.php?action=get_programs',
        type: 'POST',
        data: {domain: domain},
        dataType: 'json',
        success: function(res){
          if(res.code === 0){
            var html = '<option value="">请选择要下载的程序</option>';
            $.each(res.data, function(i, item){
              html += '<option value="' + item.id + '">' + item.name + ' (' + item.version + ')</option>';
            });
            $('select[name="program_id"]').html(html);
            form.render('select');
          }
        }
      });
    }
    
    // 选择程序
    form.on('select(program)', function(data){
      if(data.value){
        $('#downloadBtn').removeAttr('disabled');
      } else {
        $('#downloadBtn').attr('disabled', true);
      }
    });
    
    // 点击下载按钮
    $(document).on('click', '#downloadBtn', function(){
      var domain = $('input[name="domain"]').val();
      var programId = $('select[name="program_id"]').val();
      
      if(!programId){
        layer.msg('请选择要下载的程序');
        return;
      }
      
      // 显示加载中
      var loadIndex = layer.load(2);
      
      // 发送下载请求
      $.ajax({
        url: 'api/ajax.php?action=download',
        type: 'POST',
        data: {domain: domain, program_id: programId},
        dataType: 'json',
        success: function(res){
          layer.close(loadIndex);
          
          if(res.code === 0){
            // 下载成功，跳转到下载页面
            window.location.href = res.data.download_url;
          } else {
            // 失败
            layer.msg('下载失败: ' + res.msg);
          }
        },
        error: function(){
          layer.close(loadIndex);
          layer.msg('网络错误，请重试');
        }
      });
    });
  });
  </script>
</body>
</html>
```

### 2. 管理后台

管理后台结构使用Layui后台布局模板：

```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>域名授权系统管理后台</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="../assets/layui/css/layui.css">
</head>
<body>
  <div class="layui-layout layui-layout-admin">
    <!-- 头部 -->
    <div class="layui-header">
      <div class="layui-logo layui-hide-xs">域名授权系统</div>
      <ul class="layui-nav layui-layout-right">
        <li class="layui-nav-item layui-hide-xs">
          <a href="javascript:;">
            <img src="https://unpkg.com/outeres@0.0.10/img/layui/icon-v2.png" class="layui-nav-img">
            管理员
          </a>
          <dl class="layui-nav-child">
            <dd><a href="javascript:;">修改密码</a></dd>
            <dd><a href="javascript:;">退出登录</a></dd>
          </dl>
        </li>
      </ul>
    </div>
    
    <!-- 侧边菜单 -->
    <div class="layui-side layui-bg-black">
      <div class="layui-side-scroll">
        <ul class="layui-nav layui-nav-tree" lay-filter="test">
          <li class="layui-nav-item layui-this">
            <a href="javascript:;" data-url="welcome.php" data-id="1" data-title="控制台" class="site-menu">
              <i class="layui-icon">&#xe665;</i> 控制台
            </a>
          </li>
          <li class="layui-nav-item">
            <a href="javascript:;">
              <i class="layui-icon">&#xe857;</i> 域名管理
            </a>
            <dl class="layui-nav-child">
              <dd><a href="javascript:;" data-url="domains.php" data-id="2" data-title="域名列表" class="site-menu">域名列表</a></dd>
              <dd><a href="javascript:;" data-url="domain_add.php" data-id="3" data-title="添加域名" class="site-menu">添加域名</a></dd>
            </dl>
          </li>
          <li class="layui-nav-item">
            <a href="javascript:;">
              <i class="layui-icon">&#xe655;</i> 程序管理
            </a>
            <dl class="layui-nav-child">
              <dd><a href="javascript:;" data-url="programs.php" data-id="4" data-title="程序列表" class="site-menu">程序列表</a></dd>
              <dd><a href="javascript:;" data-url="program_add.php" data-id="5" data-title="添加程序" class="site-menu">添加程序</a></dd>
            </dl>
          </li>
          <li class="layui-nav-item">
            <a href="javascript:;" data-url="downloads.php" data-id="6" data-title="下载记录" class="site-menu">
              <i class="layui-icon">&#xe60e;</i> 下载记录
            </a>
          </li>
          <li class="layui-nav-item">
            <a href="javascript:;" data-url="settings.php" data-id="7" data-title="系统设置" class="site-menu">
              <i class="layui-icon">&#xe620;</i> 系统设置
            </a>
          </li>
        </ul>
      </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="layui-body">
      <div class="layui-tab" lay-filter="main-tab" lay-allowclose="true">
        <ul class="layui-tab-title">
          <li class="layui-this" lay-id="1">控制台</li>
        </ul>
        <div class="layui-tab-content">
          <div class="layui-tab-item layui-show">
            <iframe src="welcome.php" frameborder="0" class="layui-admin-iframe" id="layui-iframe-1"></iframe>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部 -->
    <div class="layui-footer">
      © 2024 域名授权系统
    </div>
  </div>
  
  <script src="../assets/layui/layui.js"></script>
  <script>
  layui.use(['element', 'layer'], function(){
    var element = layui.element;
    var $ = layui.$;
    
    // 点击菜单
    $('.site-menu').on('click', function(){
      var url = $(this).data('url');
      var id = $(this).data('id');
      var title = $(this).data('title');
      
      // 检查标签是否已存在
      var isTabExist = false;
      $('.layui-tab-title li').each(function(){
        if($(this).attr('lay-id') == id){
          isTabExist = true;
        }
      });
      
      if(!isTabExist){
        // 新增一个Tab项
        element.tabAdd('main-tab', {
          title: title,
          content: '<iframe src="' + url + '" frameborder="0" class="layui-admin-iframe" id="layui-iframe-' + id + '"></iframe>',
          id: id
        });
      }
      
      // 切换到指定Tab项
      element.tabChange('main-tab', id);
    });
  });
  </script>
  
  <style>
  .layui-layout-admin .layui-body {
    padding-bottom: 0;
  }
  .layui-tab {
    margin: 0;
  }
  .layui-tab-content {
    padding: 0;
    height: calc(100% - 40px);
  }
  .layui-tab-item {
    height: 100%;
  }
  .layui-admin-iframe {
    width: 100%;
    height: 100%;
  }
  </style>
</body>
</html>
```

### 3. 域名管理页面

```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>域名列表</title>
  <link rel="stylesheet" href="../assets/layui/css/layui.css">
</head>
<body>
  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-header">域名列表</div>
      <div class="layui-card-body">
        <div class="layui-form layui-form-pane" style="margin-bottom: 15px;">
          <div class="layui-form-item">
            <div class="layui-inline">
              <label class="layui-form-label">域名</label>
              <div class="layui-input-inline">
                <input type="text" name="domain" autocomplete="off" class="layui-input">
              </div>
            </div>
            <div class="layui-inline">
              <label class="layui-form-label">状态</label>
              <div class="layui-input-inline">
                <select name="status">
                  <option value="">全部</option>
                  <option value="1">启用</option>
                  <option value="0">禁用</option>
                </select>
              </div>
            </div>
            <div class="layui-inline">
              <button class="layui-btn" lay-submit lay-filter="search">查询</button>
              <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
          </div>
        </div>
        
        <table id="domainTable" lay-filter="domainTable"></table>
        
        <script type="text/html" id="toolbar">
          <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm" lay-event="add">添加域名</button>
            <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchDel">批量删除</button>
          </div>
        </script>
        
        <script type="text/html" id="barTool">
          <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
          <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">删除</a>
        </script>
        
        <script type="text/html" id="statusTpl">
          <input type="checkbox" name="status" value="{{d.id}}" lay-skin="switch" lay-text="启用|禁用" lay-filter="statusSwitch" {{ d.status == 1 ? 'checked' : '' }}>
        </script>
      </div>
    </div>
  </div>
  
  <script src="../assets/layui/layui.js"></script>
  <script>
  layui.use(['table', 'form', 'layer'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.$;
    
    // 渲染表格
    table.render({
      elem: '#domainTable',
      url: '../api/ajax.php?action=get_domains',
      toolbar: '#toolbar',
      defaultToolbar: ['filter', 'exports', 'print'],
      cols: [[
        {type: 'checkbox', fixed: 'left'},
        {field: 'id', title: 'ID', width: 80, sort: true},
        {field: 'domain', title: '域名', width: 200},
        {field: 'auth_code', title: '授权码', width: 300},
        {field: 'expire_time', title: '过期时间', width: 180, sort: true},
        {field: 'status', title: '状态', width: 100, templet: '#statusTpl'},
        {field: 'create_time', title: '创建时间', width: 180, sort: true},
        {field: 'update_time', title: '更新时间', width: 180, sort: true},
        {fixed: 'right', title: '操作', toolbar: '#barTool', width: 120}
      ]],
      page: true
    });
    
    // 头工具栏事件
    table.on('toolbar(domainTable)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id);
      
      switch(obj.event){
        case 'add':
          layer.open({
            type: 2,
            title: '添加域名',
            content: 'domain_add.php',
            area: ['500px', '400px'],
            end: function(){
              // 刷新表格
              table.reload('domainTable');
            }
          });
          break;
        case 'batchDel':
          var data = checkStatus.data;
          if(data.length === 0){
            layer.msg('请选择要删除的数据');
            return;
          }
          
          layer.confirm('确定删除选中的域名？', function(index){
            var ids = [];
            for(var i = 0; i < data.length; i++){
              ids.push(data[i].id);
            }
            
            // 发送删除请求
            $.ajax({
              url: '../api/ajax.php?action=delete_domains',
              type: 'POST',
              data: {ids: ids.join(',')},
              dataType: 'json',
              success: function(res){
                if(res.code === 0){
                  layer.msg('删除成功');
                  table.reload('domainTable');
                } else {
                  layer.msg('删除失败: ' + res.msg);
                }
              }
            });
            
            layer.close(index);
          });
          break;
      }
    });
    
    // 监听行工具事件
    table.on('tool(domainTable)', function(obj){
      var data = obj.data;
      
      switch(obj.event){
        case 'edit':
          layer.open({
            type: 2,
            title: '编辑域名',
            content: 'domain_edit.php?id=' + data.id,
            area: ['500px', '400px'],
            end: function(){
              // 刷新表格
              table.reload('domainTable');
            }
          });
          break;
        case 'del':
          layer.confirm('确定删除此域名？', function(index){
            // 发送删除请求
            $.ajax({
              url: '../api/ajax.php?action=delete_domain',
              type: 'POST',
              data: {id: data.id},
              dataType: 'json',
              success: function(res){
                if(res.code === 0){
                  layer.msg('删除成功');
                  obj.del();
                } else {
                  layer.msg('删除失败: ' + res.msg);
                }
              }
            });
            
            layer.close(index);
          });
          break;
      }
    });
    
    // 监听状态切换
    form.on('switch(statusSwitch)', function(obj){
      var id = this.value;
      var status = obj.elem.checked ? 1 : 0;
      
      // 发送状态更新请求
      $.ajax({
        url: '../api/ajax.php?action=update_domain_status',
        type: 'POST',
        data: {id: id, status: status},
        dataType: 'json',
        success: function(res){
          if(res.code !== 0){
            layer.msg('更新失败: ' + res.msg);
            // 恢复原状态
            $(obj.elem).prop('checked', !obj.elem.checked);
            form.render('checkbox');
          }
        }
      });
    });
    
    // 监听搜索
    form.on('submit(search)', function(data){
      table.reload('domainTable', {
        page: {
          curr: 1
        },
        where: data.field
      });
      return false;
    });
  });
  </script>
</body>
</html>
```

## 交互流程设计

### 1. 域名查询流程

```
用户输入域名 → 点击查询按钮 → 发送AJAX请求到后端 → 
显示查询结果 → 若已授权则显示下载表单 → 若未授权则显示提示信息
```

### 2. 程序下载流程

```
用户选择程序 → 点击下载按钮 → 发送AJAX请求到后端 → 
后端处理授权程序 → 返回下载链接 → 浏览器自动跳转到下载页面
```

### 3. 管理后台操作流程

```
管理员登录 → 管理域名/程序/查看记录 → 
具体操作（添加/编辑/删除域名，上传/更新程序等）
```

## 优化与兼容性

1. **移动端优化**:
   - 使用Layui的响应式布局
   - 针对小屏幕设备优化表单和表格展示

2. **性能优化**:
   - 合理使用AJAX减少页面刷新
   - 分页加载数据，避免一次加载过多内容

3. **兼容性处理**:
   - 兼容主流浏览器
   - 针对IE11等特殊浏览器提供降级处理

## 用户体验改进点

1. **操作引导**:
   - 提供清晰的操作提示
   - 使用Layui的Tips功能解释重要字段

2. **错误反馈**:
   - 友好的错误提示
   - 表单验证即时反馈

3. **状态指示**:
   - 操作过程中显示加载状态
   - 操作结果明确反馈

## 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|-----|------|---------|-------|
| v0.1 | [2024-08-19 11:20:00 +08:00] | 初始UI设计 | UI/UX |