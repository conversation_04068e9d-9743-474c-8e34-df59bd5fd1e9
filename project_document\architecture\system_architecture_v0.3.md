# 域名授权系统架构设计 v0.3

创建时间: [2024-08-19 10:25:00 +08:00]
创建者: 系统架构师(AR)
最后更新: [2024-08-19 12:10:00 +08:00]

## 系统概述
域名授权系统是一个用于管理软件授权的Web应用，允许用户查询域名授权状态并下载授权后的程序。系统采用PHP7.4开发，使用MySQL5.7作为数据库，前端界面基于Layui框架实现。

## 架构设计（混合架构）
系统采用混合架构，结合MVC和函数式编程的优点：
- **模型(Model)**: 简化的数据操作类，主要处理数据库交互
- **视图(View)**: 基于Layui实现的用户界面
- **控制器(Controller)**: 简化的路由和请求处理，以函数为主
- **工具类**: 实用工具函数集合，包含授权码生成、文件操作等

### 目录结构
```
/auth/
  ├── index.php              # 入口文件
  ├── config.php             # 配置文件
  ├── includes/              # 核心功能目录
  │   ├── functions.php      # 通用函数
  │   ├── db.php             # 数据库操作
  │   ├── auth.php           # 授权相关函数
  │   └── ...
  ├── admin/                 # 管理员界面
  │   ├── index.php          # 管理面板
  │   ├── domains.php        # 域名管理
  │   ├── download.php       # 下载管理
  │   └── ...
  ├── api/                   # API接口
  │   ├── index.php          # API入口
  │   └── ajax.php           # AJAX处理
  ├── programs/              # 程序源文件存放目录
  │   └── ...                # 需要授权的程序源文件
  ├── temp/                  # 临时文件目录
  │   └── ...                # 临时生成的授权程序和压缩包
  ├── assets/                # 静态资源
  │   └── layui/             # Layui框架
  └── templates/             # 前端模板
      ├── header.php
      ├── footer.php
      └── ...
```

### 数据库设计
主要表结构:

#### 域名表(domains)
```sql
CREATE TABLE `domains` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `auth_code` varchar(255) NOT NULL COMMENT '授权码',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否试用状态:0否,1是',
  `trial_start_time` datetime DEFAULT NULL COMMENT '试用开始时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `domain` (`domain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 下载记录表(downloads)
```sql
CREATE TABLE `downloads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `auth_code` varchar(255) NOT NULL COMMENT '授权码',
  `download_time` datetime NOT NULL COMMENT '下载时间',
  `ip` varchar(50) NOT NULL COMMENT 'IP地址',
  `program_id` int(11) NOT NULL COMMENT '程序ID',
  `is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否试用下载:0否,1是',
  PRIMARY KEY (`id`),
  KEY `idx_domain` (`domain`),
  KEY `idx_download_time` (`download_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 程序表(programs)
```sql
CREATE TABLE `programs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '程序名称',
  `version` varchar(50) NOT NULL COMMENT '程序版本',
  `path` varchar(255) NOT NULL COMMENT '程序存放路径',
  `description` text COMMENT '程序描述',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### API接口设计
系统将提供以下API接口:

1. **域名查询接口**
   - 请求方式: GET/POST
   - 接口路径: `/api/ajax.php?action=check_domain`
   - 参数: `domain` (域名)
   - 返回格式: JSON
   - 响应示例:
     ```json
     {
       "code": 0,
       "msg": "success",
       "data": {
         "is_auth": true,
         "expire_time": "2025-08-19 00:00:00",
         "is_trial": false
       }
     }
     ```

2. **授权程序下载接口**
   - 请求方式: GET/POST
   - 接口路径: `/api/ajax.php?action=download`
   - 参数: 
     - `domain` (域名)
     - `program_id` (可选，程序ID)
   - 返回格式: JSON/文件下载
   - 响应示例:
     ```json
     {
       "code": 0,
       "msg": "success",
       "data": {
         "download_url": "/download.php?token=xxxxx"
       }
     }
     ```

3. **试用激活接口**
   - 请求方式: POST
   - 接口路径: `/api/ajax.php?action=activate_trial`
   - 参数: `domain` (域名)
   - 返回格式: JSON
   - 响应示例:
     ```json
     {
       "code": 0,
       "msg": "试用激活成功",
       "data": {
         "domain": "example.com",
         "trial_start": "2024-08-19 12:00:00",
         "trial_end": "2024-08-22 12:00:00",
         "remaining_days": 3
       }
     }
     ```

### 授权程序处理流程
1. **程序源文件管理**
   - 程序源文件存放在 `/programs/` 目录
   - 管理员可通过后台上传新版本程序
   - 系统记录程序信息到 programs 表

2. **授权码生成与程序打包**
   - 用户请求下载授权程序时，系统执行以下步骤:
     a. 验证域名授权状态（包括正式授权和试用授权）
     b. 从 `/programs/` 复制原始程序到 `/temp/` 目录下的临时文件夹
     c. 生成唯一授权码（正式授权或试用授权码）并更新domains表
     d. 修改临时目录中程序的 `/includes/authcode.php` 文件，更新 $authcode 变量值
     e. 将临时目录中的程序打包成zip文件
     f. 提供zip文件下载链接
     g. 记录下载信息到downloads表（包括是否为试用下载）
     h. 设置定时任务清理temp目录中的临时文件(24小时后)

3. **下载安全控制**
   - 生成一次性下载链接，使用token参数控制访问
   - 限制下载次数和有效期
   - 下载前再次验证域名授权状态

### 试用功能设计
1. **试用激活流程**
   - 用户输入域名查询后，如未授权或已过期，系统显示试用选项
   - 用户点击"立即试用"按钮，系统调用试用激活接口
   - 系统创建或更新域名记录，设置为试用状态，记录试用开始时间
   - 试用期为3天，从激活时刻开始计算

2. **试用状态验证**
   - 系统在验证域名授权状态时，同时检查是否为试用状态
   - 如为试用状态，计算剩余试用天数并返回
   - 试用到期后，系统将提示用户试用已结束，需要购买正式授权

3. **试用授权码生成**
   - 试用授权码生成与正式授权码类似，但添加试用标记
   - 试用授权码内置过期时间（激活后3天）
   - 授权码生成时会区分是正式授权还是试用授权

4. **试用限制**
   - 每个域名只能试用一次
   - 试用到期后不能再次申请试用
   - 试用期间可以正常下载和使用程序，但3天后自动失效

### 安全考虑
1. 防止SQL注入: 所有数据库操作使用参数化查询
2. XSS防护: 输入输出过滤
3. CSRF防护: 表单Token验证
4. 授权码加密: 采用不可逆加密算法
5. 访问控制: 基于角色的权限管理
6. 临时文件安全: 使用随机目录名，限制直接访问
7. 下载链接保护: 使用一次性token，防止链接泄露导致非授权下载

## 创新点
1. **自适应授权码生成机制**
   - 结合域名特征、时间戳和随机因子生成授权码
   - 授权码可包含过期信息，即使脱离系统也能进行基础验证
   - 区分正式授权码和试用授权码

2. **分布式授权验证**
   - 授权程序可在无网络环境下进行本地初步验证
   - 在有网络环境下可向授权中心进行二次验证，提升安全性

3. **授权状态监控**
   - 为授权程序提供可选的状态上报机制
   - 管理员可查看授权程序的使用情况和分布

4. **灵活的试用机制**
   - 提供3天免费试用期，增加用户转化率
   - 试用状态与正式授权状态分离，便于管理和统计
   - 试用到期后自动提示用户购买正式授权

## 适配PHP7.4特性
1. 利用PHP7.4的类型声明增强功能
2. 使用PHP7.4的箭头函数简化代码
3. 合理使用空合并赋值运算符 (??=)
4. 适配PHP7.4的OPcache改进

## 更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|-----|------|---------|-------|
| v0.1 | [2024-08-19 10:25:00 +08:00] | 初始架构设计 | AR |
| v0.2 | [2024-08-19 10:50:00 +08:00] | 更新架构设计，添加程序管理与下载流程 | AR |
| v0.3 | [2024-08-19 12:10:00 +08:00] | 添加试用功能设计 | AR |