<?php
/**
 * 管理后台UI相关通用函数
 */

/**
 * 渲染表格操作按钮
 * 
 * @param array $buttons 按钮配置数组
 * @return string HTML代码
 */
function renderTableButtons($buttons) {
    $html = '<div class="layui-btn-group">';
    foreach ($buttons as $btn) {
        $class = isset($btn['class']) ? ' ' . $btn['class'] : '';
        $icon = isset($btn['icon']) ? '<i class="layui-icon ' . $btn['icon'] . '"></i> ' : '';
        $html .= '<button class="layui-btn' . $class . '" id="' . $btn['id'] . '">' . $icon . $btn['text'] . '</button>';
    }
    $html .= '</div>';
    
    return $html;
}

/**
 * 生成表单令牌隐藏域
 * 
 * @return string HTML代码
 */
function renderCsrfTokenField() {
    $token = generateCsrfToken();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}

/**
 * 渲染分页控件
 * 
 * @param int $total 总记录数
 * @param int $page 当前页码
 * @param int $limit 每页记录数
 * @param string $url 分页URL
 * @return string HTML代码
 */
function renderPagination($total, $page, $limit, $url) {
    $pages = ceil($total / $limit);
    
    if ($pages <= 1) {
        return '';
    }
    
    $html = '<div class="layui-box layui-laypage layui-laypage-default">';
    
    // 上一页
    if ($page > 1) {
        $html .= '<a href="' . $url . '?page=' . ($page - 1) . '&limit=' . $limit . '" class="layui-laypage-prev">上一页</a>';
    } else {
        $html .= '<a href="javascript:;" class="layui-laypage-prev layui-disabled">上一页</a>';
    }
    
    // 页码
    $start = max(1, $page - 2);
    $end = min($pages, $page + 2);
    
    if ($start > 1) {
        $html .= '<a href="' . $url . '?page=1&limit=' . $limit . '">1</a>';
        if ($start > 2) {
            $html .= '<span class="layui-laypage-spr">…</span>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $page) {
            $html .= '<span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>' . $i . '</em></span>';
        } else {
            $html .= '<a href="' . $url . '?page=' . $i . '&limit=' . $limit . '">' . $i . '</a>';
        }
    }
    
    if ($end < $pages) {
        if ($end < $pages - 1) {
            $html .= '<span class="layui-laypage-spr">…</span>';
        }
        $html .= '<a href="' . $url . '?page=' . $pages . '&limit=' . $limit . '">' . $pages . '</a>';
    }
    
    // 下一页
    if ($page < $pages) {
        $html .= '<a href="' . $url . '?page=' . ($page + 1) . '&limit=' . $limit . '" class="layui-laypage-next">下一页</a>';
    } else {
        $html .= '<a href="javascript:;" class="layui-laypage-next layui-disabled">下一页</a>';
    }
    
    $html .= '</div>';
    
    return $html;
}