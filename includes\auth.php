<?php
/**
 * 授权相关函数
 */

// 授权码模板 - 用于生成authcode.php文件
if (!defined('AUTH_CODE_TEMPLATE')) {
    define('AUTH_CODE_TEMPLATE', '<?php
/**
 * 授权码验证文件
 * 生成时间: %s
 * 
 * 警告：请勿修改本文件内容，否则可能导致授权失效
 */

// 授权码 - 由授权系统自动填充
$authcode = "%s";

// 其他授权验证代码需要被包含在被授权程序中
// 请参考授权系统提供的SDK示例进行授权验证
?>');
}

/**
 * 生成授权码
 * 
 * @param string $domain 域名
 * @param string $expireTime 过期时间 格式：Y-m-d H:i:s
 * @param bool $isTrial 是否为试用授权
 * @return string 授权码，格式为32位MD5
 */
function generateAuthCode($domain, $expireTime, $isTrial = false) {
    // 基础数据 - 使用授权关键信息
    $expireTimestamp = strtotime($expireTime);
    $trialFlag = $isTrial ? '1' : '0';
    $randomSalt = substr(bin2hex(random_bytes(8)), 0, 8); // 8位随机盐
    
    // 组合授权码源数据
    $authSource = $domain . '|' . $expireTimestamp . '|' . $trialFlag . '|' . AUTH_KEY . '|' . $randomSalt;
    
    // 生成32位MD5授权码
    $authCode = md5($authSource);
    
    return $authCode;
}

/**
 * 解析授权码
 * 
 * 注意：由于新授权码采用MD5格式，无法直接解码获取内部信息
 * 应该直接通过数据库查询相关授权信息
 * 
 * @param string $authCode 授权码
 * @return array|false 授权信息数组或失败返回false
 */
function decodeAuthCode($authCode) {
    // 对于MD5格式的授权码，需要通过数据库查询关联信息
    $db = DB::getInstance();
    
    // 通过授权码查询关联的域名信息
    $domainInfo = $db->get('domains', ['auth_code' => $authCode]);
    
    if (!$domainInfo) {
        error_log('授权码无效或未找到匹配记录: ' . $authCode);
        return false;
    }
    
    // 返回授权信息
    return [
        'domain' => $domainInfo['domain'],
        'expire_time' => strtotime($domainInfo['expire_time']),
        'is_trial' => ($domainInfo['is_trial'] == 1),
        'timestamp' => strtotime($domainInfo['update_time'])
    ];
    
    // 以下为旧授权码解析逻辑，保留以便向后兼容
    // 判断是否为旧格式授权码（含有'_'、'-'等Base64特殊字符替换）
    if (strpos($authCode, '_') !== false || strpos($authCode, '-') !== false) {
        try {
            // 恢复Base64编码
            $base64 = str_replace(['_', '-'], ['+', '/'], $authCode);
            $base64 = $base64 . substr('===', (strlen($base64) % 4));
            
            // 获取域名（用于生成初始向量）
            $domain = $_SERVER['HTTP_HOST'] ?? '';
            
            // 解密数据
            $decrypted = openssl_decrypt(
                base64_decode($base64),
                'AES-256-CBC',
                AUTH_KEY,
                0,
                substr(md5(AUTH_KEY . $domain), 0, 16) // 使用域名特征作为初始向量
            );
            
            if ($decrypted === false) {
                return false;
            }
            
            // 反序列化数据
            $data = unserialize($decrypted);
            
            if (!is_array($data) || !isset($data['domain'], $data['expire'], $data['fingerprint'])) {
                return false;
            }
            
            // 验证域名 - 支持子域名
            $currentDomainRoot = getDomainRoot($domain);
            $authDomainRoot = getDomainRoot($data['domain']);
            
            if ($currentDomainRoot !== $authDomainRoot) {
                return false;
            }
            
            // 验证特征码
            $fingerprint = md5($data['domain'] . $data['expire'] . AUTH_KEY);
            if ($fingerprint !== $data['fingerprint']) {
                return false;
            }
            
            // 返回处理后的数据
            return [
                'domain' => $data['domain'],
                'expire_time' => $data['expire'],
                'is_trial' => ($data['trial'] == 1),
                'timestamp' => $data['timestamp']
            ];
        } catch (Exception $e) {
            error_log('旧授权码解析错误: ' . $e->getMessage());
            return false;
        }
    }
    
    return false;
}

/**
 * 获取域名的根域名
 * 例如：www.example.com 和 sub.example.com 都返回 example.com
 * 
 * @param string $domain 完整域名
 * @return string 根域名
 */
function getDomainRoot($domain) {
    // 移除可能存在的端口号
    $domain = preg_replace('/:\d+$/', '', $domain);
    
    // 分割域名部分
    $parts = explode('.', $domain);
    $partsCount = count($parts);
    
    // 对于像 .co.uk 这样的特殊顶级域名，需要特殊处理
    // 这里使用简化的逻辑，仅考虑常见情况
    if ($partsCount > 2) {
        // 检查是否是特殊顶级域名 (.co.uk, .com.cn 等)
        $specialTLDs = ['co.uk', 'com.cn', 'net.cn', 'org.cn', 'gov.cn', 'ac.cn', 'co.jp', 'co.nz', 'co.za'];
        $lastTwo = $parts[$partsCount - 2] . '.' . $parts[$partsCount - 1];
        
        if (in_array($lastTwo, $specialTLDs)) {
            // 如果是特殊顶级域名，取最后三段
            if ($partsCount >= 3) {
                return $parts[$partsCount - 3] . '.' . $lastTwo;
            }
        } else {
            // 否则取最后两段
            return $parts[$partsCount - 2] . '.' . $parts[$partsCount - 1];
        }
    }
    
    // 如果只有两段或更少，则返回整个域名
    return $domain;
}

/**
 * 验证域名授权状态
 * 
 * @param string $domain 域名
 * @return array 授权状态信息
 */
function checkDomainAuth($domain) {
    $db = DB::getInstance();
    // 使用domains表，与其他地方保持一致
    $domainInfo = $db->get('domains', ['domain' => $domain]);
    
    $result = [
        'is_auth' => false,
        'expire_time' => null,
        'is_trial' => false,
        'remaining_days' => 0,
        'msg' => '域名未授权'
    ];
    
    if (!$domainInfo) {
        return $result;
    }
    
    // 检查状态
    if ($domainInfo['status'] == 0) {
        $result['msg'] = '域名授权已被禁用';
        return $result;
    }
    
    // 检查过期时间
    $expireTime = strtotime($domainInfo['expire_time']);
    $now = time();
    $remainingSeconds = $expireTime - $now;
    
    if ($remainingSeconds <= 0) {
        $result['msg'] = '域名授权已过期';
        $result['expire_time'] = $domainInfo['expire_time'];
        return $result;
    }
    
    // 授权有效
    $result['is_auth'] = true;
    $result['expire_time'] = $domainInfo['expire_time'];
    $result['is_trial'] = ($domainInfo['is_trial'] == 1);
    $result['remaining_days'] = ceil($remainingSeconds / 86400); // 转换为天数
    $result['msg'] = '域名授权有效';
    
    return $result;
}

/**
 * 激活试用
 * 
 * @param string $domain 域名
 * @return array 激活结果
 */
function activateTrial($domain) {
    $db = DB::getInstance();
    // 使用domains表，保持一致性
    $domainInfo = $db->get('domains', ['domain' => $domain]);
    
    $result = [
        'code' => 1,
        'msg' => '',
        'data' => []
    ];
    
    // 检查域名是否已存在
    if ($domainInfo) {
        // 如果已经是正式授权，不允许试用
        if ($domainInfo['is_trial'] == 0 && strtotime($domainInfo['expire_time']) > time()) {
            $result['msg'] = '该域名已有正式授权，无需试用';
            return $result;
        }
        
        // 如果已经试用过，不允许再次试用
        if ($domainInfo['is_trial'] == 1 && $domainInfo['trial_start_time']) {
            $trialEnd = strtotime($domainInfo['trial_start_time']) + (TRIAL_DAYS * 86400);
            
            // 如果试用期已过，提示购买正式版
            if (time() > $trialEnd) {
                $result['msg'] = '试用期已结束，请购买正式授权';
                return $result;
            }
            
            // 如果试用期还没过，返回当前试用信息
            $trialEndDate = date('Y-m-d H:i:s', $trialEnd);
            $remainingDays = ceil(($trialEnd - time()) / 86400);
            
            $result['code'] = 0;
            $result['msg'] = '已激活试用';
            $result['data'] = [
                'domain' => $domain,
                'trial_start' => $domainInfo['trial_start_time'],
                'trial_end' => $trialEndDate,
                'remaining_days' => $remainingDays,
                'auth_code' => $domainInfo['auth_code']
            ];
            return $result;
        }
    }
    
    // 创建或更新试用记录
    $now = date('Y-m-d H:i:s');
    $trialEnd = date('Y-m-d H:i:s', strtotime("+".TRIAL_DAYS." days"));
    
    try {
        // 生成试用授权码
        $authCode = generateAuthCode($domain, $trialEnd, true);
        
        $data = [
            'domain' => $domain,
            'auth_code' => $authCode,
            'status' => 1,
            'is_trial' => 1,
            'trial_start_time' => $now,
            'expire_time' => $trialEnd,
            'update_time' => $now
        ];
        
        if ($domainInfo) {
            // 更新现有记录
            $db->update('domains', $data, ['id' => $domainInfo['id']]);
        } else {
            // 创建新记录
            $data['create_time'] = $now;
            $db->insert('domains', $data);
        }
        
        $result['code'] = 0;
        $result['msg'] = '试用激活成功';
        $result['data'] = [
            'domain' => $domain,
            'trial_start' => $now,
            'trial_end' => $trialEnd,
            'remaining_days' => TRIAL_DAYS,
            'auth_code' => $authCode
        ];
        
    } catch (Exception $e) {
        error_log('试用激活失败: ' . $e->getMessage());
        $result['code'] = 1;
        $result['msg'] = '试用激活失败，请稍后再试';
    }
    
    return $result;
}

/**
 * 处理程序下载
 * 
 * @param string $domain 域名
 * @param int $programId 程序ID，默认为1
 * @return array 下载信息
 */
function processDownload($domain, $programId = 1) {
    $db = DB::getInstance();
    
    // 检查域名授权状态
    $authStatus = checkDomainAuth($domain);
    
    $result = [
        'code' => 1,
        'msg' => '',
        'data' => []
    ];
    
    // 如果域名未授权或已过期，返回错误
    if (!$authStatus['is_auth']) {
        $result['msg'] = $authStatus['msg'];
        return $result;
    }
    
    // 获取程序信息
    $program = $db->get('programs', ['id' => $programId]);
    if (!$program) {
        $result['msg'] = '程序不存在';
        return $result;
    }
    
    // 创建临时目录
    $tempDir = TEMP_PATH . 'download_' . md5($domain . time() . rand(1000, 9999));
    if (!mkdir($tempDir, 0755, true)) {
        $result['msg'] = '创建临时目录失败';
        return $result;
    }
    
    // 复制程序文件到临时目录
    $programPath = PROGRAM_PATH . $program['path'];
    if (!is_dir($programPath)) {
        rmdir($tempDir);
        $result['msg'] = '程序源文件不存在';
        return $result;
    }
    
    // 递归复制文件夹
    copyDir($programPath, $tempDir);
    
    // 获取域名信息和授权码
    $domainInfo = $db->get('domains', ['domain' => $domain]);
    $authCode = $domainInfo['auth_code'];
    
    // 修改授权码文件
    $authCodePath = $tempDir . '/includes/authcode.php';
    if (file_exists($authCodePath)) {
        $authCodeContent = sprintf(AUTH_CODE_TEMPLATE, date('Y-m-d H:i:s'), $authCode);
        file_put_contents($authCodePath, $authCodeContent);
    } else {
        // 创建includes目录
        if (!is_dir($tempDir . '/includes')) {
            mkdir($tempDir . '/includes', 0755, true);
        }
        $authCodeContent = sprintf(AUTH_CODE_TEMPLATE, date('Y-m-d H:i:s'), $authCode);
        file_put_contents($authCodePath, $authCodeContent);
    }
    
    // 创建zip文件
    $zipName = 'program_' . $domain . '_' . date('YmdHis') . '.zip';
    $zipPath = TEMP_PATH . $zipName;
    
    $zip = new ZipArchive();
    if ($zip->open($zipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
        rmdirRecursive($tempDir);
        $result['msg'] = '创建ZIP文件失败';
        return $result;
    }
    
    // 添加文件到ZIP
    addDirToZip($zip, $tempDir, basename($tempDir));
    $zip->close();
    
    // 清理临时目录
    rmdirRecursive($tempDir);
    
    // 生成下载token
    $token = md5($domain . $zipName . time() . rand(1000, 9999));
    
    // 保存下载记录
    $db->insert('downloads', [
        'domain' => $domain,
        'auth_code' => $authCode,
        'download_time' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'program_id' => $programId,
        'is_trial' => $domainInfo['is_trial']
    ]);
    
    // 设置下载信息到SESSION
    $_SESSION['download_token'] = $token;
    $_SESSION['download_file'] = $zipPath;
    $_SESSION['download_name'] = $zipName;
    $_SESSION['download_expire'] = time() + 3600; // 1小时后过期
    
    $result['code'] = 0;
    $result['msg'] = '处理成功';
    $result['data'] = [
        'download_url' => SITE_URL . 'download.php?token=' . $token,
        'expire_time' => date('Y-m-d H:i:s', $_SESSION['download_expire'])
    ];
    
    return $result;
}

/**
 * 递归复制目录
 * 
 * @param string $source 源目录
 * @param string $dest 目标目录
 * @return bool 成功返回true，失败返回false
 */
function copyDir($source, $dest) {
    if (!is_dir($source)) {
        return false;
    }
    
    if (!is_dir($dest)) {
        mkdir($dest, 0755, true);
    }
    
    $dir = opendir($source);
    while (($file = readdir($dir)) !== false) {
        if ($file == '.' || $file == '..') {
            continue;
        }
        
        $srcFile = $source . '/' . $file;
        $destFile = $dest . '/' . $file;
        
        if (is_dir($srcFile)) {
            copyDir($srcFile, $destFile);
        } else {
            copy($srcFile, $destFile);
        }
    }
    closedir($dir);
    
    return true;
}

/**
 * 递归删除目录
 * 
 * @param string $dir 目录路径
 * @return bool 成功返回true，失败返回false
 */
function rmdirRecursive($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        is_dir($path) ? rmdirRecursive($path) : unlink($path);
    }
    
    return rmdir($dir);
}

/**
 * 添加目录到ZIP文件
 * 
 * @param ZipArchive $zip ZIP对象
 * @param string $dir 目录路径
 * @param string $zipDir ZIP内目录名
 */
function addDirToZip($zip, $dir, $zipDir = '') {
    if (is_dir($dir)) {
        if ($zipDir !== '') {
            $zip->addEmptyDir($zipDir);
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            $zipPath = $zipDir !== '' ? $zipDir . '/' . $file : $file;
            
            if (is_dir($path)) {
                addDirToZip($zip, $path, $zipPath);
            } else {
                $zip->addFile($path, $zipPath);
            }
        }
    }
}