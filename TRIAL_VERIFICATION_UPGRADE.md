# 🔄 试用期验证功能升级

## 📋 概述

本次升级为 `verify_auth` 接口添加了完整的试用期判断逻辑，基于数据库的 `trial_start_time` 字段和配置文件中的 `TRIAL_DAYS` 设置，实现了精确的试用期管理。

## 🎯 升级目标

- ✅ 基于 `trial_start_time` 字段计算试用期
- ✅ 使用 `config.php` 中的 `TRIAL_DAYS` 配置
- ✅ 区分试用过期和正式授权过期
- ✅ 返回详细的试用期信息
- ✅ 保持向后兼容性

## 🔧 技术实现

### 1. 配置文件更新

**文件**: `config.php`
```php
// 试用配置
define('TRIAL_DAYS', 3); // 试用天数
```

### 2. 数据库表结构

**表**: `domains`
```sql
`trial_start_time` datetime DEFAULT NULL COMMENT '试用开始时间'
`is_trial` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否试用状态:0否,1是'
```

### 3. API接口升级

**文件**: `api/ajax.php` - `verify_auth` 接口

#### 3.1 试用期计算逻辑
```php
// 如果是试用授权，需要特殊处理试用期
if ($domainInfo['is_trial'] == 1) {
    // 计算试用期结束时间
    $trialStartTime = strtotime($domainInfo['trial_start_time']);
    $trialEndTime = $trialStartTime + (TRIAL_DAYS * 24 * 60 * 60);
    
    // 检查试用期是否已过期
    if ($currentTime > $trialEndTime) {
        jsonResponse(1, '试用期已结束', [
            'is_trial' => true,
            'trial_start_time' => $domainInfo['trial_start_time'],
            'trial_end_time' => date('Y-m-d H:i:s', $trialEndTime),
            'trial_days' => TRIAL_DAYS
        ]);
    }
}
```

#### 3.2 响应数据增强
```php
// 如果是试用授权，添加试用相关信息
if ($domainInfo['is_trial'] == 1) {
    $responseData['trial_start_time'] = $domainInfo['trial_start_time'];
    $responseData['trial_end_time'] = date('Y-m-d H:i:s', $trialEndTime);
    $responseData['trial_days'] = TRIAL_DAYS;
    $responseData['trial_remaining_days'] = $remainingDays;
}
```

## 📊 API响应格式

### 试用期内（有效）
```json
{
    "code": 0,
    "msg": "授权有效",
    "data": {
        "domain": "example.com",
        "expire_time": "2024-12-25 23:59:59",
        "is_trial": 1,
        "remaining_days": 2,
        "trial_start_time": "2024-12-22 10:00:00",
        "trial_end_time": "2024-12-25 10:00:00",
        "trial_days": 3,
        "trial_remaining_days": 2
    }
}
```

### 试用期已过期
```json
{
    "code": 1,
    "msg": "试用期已结束",
    "data": {
        "is_trial": true,
        "trial_start_time": "2024-12-19 10:00:00",
        "trial_end_time": "2024-12-22 10:00:00",
        "trial_days": 3
    }
}
```

### 正式授权过期
```json
{
    "code": 1,
    "msg": "授权已过期",
    "data": {}
}
```

## 🔄 处理流程

```
1. 接收验证请求
   ↓
2. 查询域名信息
   ↓
3. 检查是否为试用授权 (is_trial = 1)
   ↓
4a. 试用授权:
    - 检查 trial_start_time 是否存在
    - 计算试用期结束时间 = trial_start_time + TRIAL_DAYS
    - 比较当前时间与试用期结束时间
    - 返回相应结果
   ↓
4b. 正式授权:
    - 检查 expire_time
    - 返回相应结果
```

## 🧪 测试工具

### 1. 后端测试脚本
**文件**: `test_trial_verification.php`
- 创建测试数据
- 验证试用期计算逻辑
- 模拟各种场景
- 清理测试数据

### 2. 前端测试工具
**文件**: `test_api_verify.html`
- 可视化API测试界面
- 预设测试场景
- 实时查看API响应
- 支持多种验证类型

## 📈 使用示例

### PHP客户端调用
```php
$postData = [
    'action' => 'verify_auth',
    'auth_code' => 'YOUR_AUTH_CODE',
    'domain' => 'your-domain.com',
    'client_ip' => $_SERVER['REMOTE_ADDR'],
    'timestamp' => time(),
    'verify_token' => md5($authCode . date('Ymd') . $domain)
];

$response = httpRequest('/api/ajax.php', $postData);
$result = json_decode($response, true);

if ($result['code'] === 0) {
    // 授权有效
    if ($result['data']['is_trial']) {
        echo "试用期剩余: " . $result['data']['trial_remaining_days'] . " 天";
    } else {
        echo "正式授权，剩余: " . $result['data']['remaining_days'] . " 天";
    }
} else {
    // 授权无效或过期
    if (isset($result['data']['is_trial']) && $result['data']['is_trial']) {
        echo "试用期已结束，请购买正式授权";
    } else {
        echo "授权已过期，请续费";
    }
}
```

## 🔒 安全考虑

1. **数据验证**: 严格验证 `trial_start_time` 字段
2. **时间同步**: 确保服务器时间准确
3. **防篡改**: 试用期基于服务器端计算，客户端无法篡改
4. **日志记录**: 记录所有验证请求和结果

## 🚀 部署说明

1. **更新配置**: 确保 `config.php` 中有 `TRIAL_DAYS` 定义
2. **数据库检查**: 确认 `domains` 表有 `trial_start_time` 字段
3. **API测试**: 使用提供的测试工具验证功能
4. **客户端更新**: 更新客户端代码以处理新的响应格式

## 📝 注意事项

1. **时区设置**: 确保服务器时区设置正确
2. **数据迁移**: 现有试用数据可能需要补充 `trial_start_time`
3. **缓存更新**: 客户端缓存可能需要清理以获取新格式数据
4. **监控告警**: 建议添加试用期到期提醒功能

## ✅ 验证清单

- [ ] 配置文件包含 `TRIAL_DAYS` 定义
- [ ] 数据库表包含 `trial_start_time` 字段
- [ ] API接口正确处理试用期逻辑
- [ ] 试用期内返回正确的剩余天数
- [ ] 试用期过期返回正确的错误信息
- [ ] 正式授权不受影响
- [ ] 测试工具运行正常
- [ ] 客户端代码已更新

## 🎉 升级完成

试用期验证功能已成功升级，现在可以：
- 精确计算试用期剩余时间
- 区分不同类型的授权过期
- 提供详细的试用期信息
- 支持灵活的试用天数配置

系统现在具备了完整的试用期管理能力！
