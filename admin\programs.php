<?php
/**
 * 程序管理页面
 */
// 包含头部
require_once 'common/header.php';

// 生成CSRF令牌
$csrfToken = generateCsrfToken();
?>

<!-- 操作按钮 -->
<div class="layui-btn-group">
    <button class="layui-btn" id="add-program-btn">添加程序</button>
    <button class="layui-btn layui-btn-danger" id="batch-delete-btn">批量删除</button>
</div>

<!-- 数据表格 -->
<table id="program-table" lay-filter="program-table"></table>

<!-- 表格操作按钮模板 -->
<script type="text/html" id="table-operation">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<!-- 程序添加/编辑表单 -->
<script type="text/html" id="program-form">
    <form class="layui-form" lay-filter="program-form" style="padding: 20px;">
        <input type="hidden" name="id" value="">
        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
        
        <div class="layui-form-item">
            <label class="layui-form-label">名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" lay-verify="required" autocomplete="off" placeholder="请输入程序名称" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">版本</label>
            <div class="layui-input-block">
                <input type="text" name="version" lay-verify="required" autocomplete="off" placeholder="请输入版本号" class="layui-input">
            </div>
        </div>
        
        <div class="layui-form-item">
            <label class="layui-form-label">路径</label>
            <div class="layui-input-block">
                <input type="text" name="path" lay-verify="required" autocomplete="off" placeholder="请输入程序路径" class="layui-input">
                <div class="layui-form-mid layui-word-aux">相对于programs目录的路径，如：example_program</div>
            </div>
        </div>
        
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">描述</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入程序描述" class="layui-textarea"></textarea>
            </div>
        </div>
        
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="program-submit">提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</script>

<!-- 程序上传表单 -->
<script type="text/html" id="upload-form">
    <div style="padding: 20px;">
        <div class="layui-upload">
            <button type="button" class="layui-btn" id="program-upload">选择文件</button>
            <div class="layui-upload-list">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="upload-list"></tbody>
                </table>
            </div>
            <button type="button" class="layui-btn" id="upload-start">开始上传</button>
        </div>
        <div class="layui-form-mid layui-word-aux">
            <p>1. 请上传ZIP格式的程序文件</p>
            <p>2. 上传后系统会自动解压到programs目录</p>
            <p>3. 程序结构必须包含includes/authcode.php文件</p>
        </div>
    </div>
</script>

<script>
layui.use(['table', 'form', 'layer', 'upload'], function(){
    var table = layui.table;
    var form = layui.form;
    var layer = layui.layer;
    var upload = layui.upload;
    var $ = layui.jquery;
    
    // 表格渲染
    var programTable = table.render({
        elem: '#program-table',
        url: 'ajax.php?action=get_programs',
        page: true,
        cols: [[
            {type: 'checkbox'},
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'name', title: '程序名称', width: 150},
            {field: 'version', title: '版本', width: 100},
            {field: 'path', title: '路径', width: 150},
            {field: 'description', title: '描述'},
            {field: 'create_time', title: '创建时间', width: 170, sort: true},
            {field: 'update_time', title: '更新时间', width: 170, sort: true},
            {fixed: 'right', title: '操作', toolbar: '#table-operation', width: 120}
        ]],
        response: {
            statusCode: 0
        },
        parseData: function(res){
            return {
                "code": res.code,
                "msg": res.msg,
                "count": res.data.count,
                "data": res.data.list
            };
        }
    });
    
    // 添加程序按钮点击事件
    $('#add-program-btn').on('click', function(){
        layer.confirm('请选择添加方式', {
            btn: ['手动添加', '上传程序包']
        }, function(){
            // 手动添加
            openProgramForm();
        }, function(){
            // 上传程序包
            openUploadForm();
        });
    });
    
    // 批量删除按钮点击事件
    $('#batch-delete-btn').on('click', function(){
        var checkStatus = table.checkStatus('program-table');
        var data = checkStatus.data;
        
        if(data.length === 0){
            layer.msg('请选择要删除的记录');
            return;
        }
        
        layer.confirm('确定删除选中的' + data.length + '条记录吗？', function(index){
            var ids = [];
            for(var i = 0; i < data.length; i++){
                ids.push(data[i].id);
            }
            
            // 发送删除请求
            $.ajax({
                url: 'ajax.php?action=batch_delete_programs',
                type: 'POST',
                data: {
                    ids: ids,
                    csrf_token: '<?php echo $csrfToken; ?>'
                },
                dataType: 'json',
                success: function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1});
                        // 刷新表格
                        programTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
            
            layer.close(index);
        });
    });
    
    // 监听工具条
    table.on('tool(program-table)', function(obj){
        var data = obj.data;
        
        if(obj.event === 'edit'){
            // 打开编辑表单
            openProgramForm(data);
        } else if(obj.event === 'del'){
            layer.confirm('确定删除该记录吗？', function(index){
                // 发送删除请求
                $.ajax({
                    url: 'ajax.php?action=delete_program',
                    type: 'POST',
                    data: {
                        id: data.id,
                        csrf_token: '<?php echo $csrfToken; ?>'
                    },
                    dataType: 'json',
                    success: function(res){
                        if(res.code === 0){
                            layer.msg(res.msg, {icon: 1});
                            obj.del();
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                
                layer.close(index);
            });
        }
    });
    
    // 监听表单提交
    form.on('submit(program-submit)', function(data){
        var field = data.field;
        var url = field.id ? 'ajax.php?action=update_program' : 'ajax.php?action=add_program';
        
        // 发送请求
        $.ajax({
            url: url,
            type: 'POST',
            data: field,
            dataType: 'json',
            success: function(res){
                if(res.code === 0){
                    layer.msg(res.msg, {icon: 1});
                    // 关闭表单
                    layer.closeAll('page');
                    // 刷新表格
                    programTable.reload();
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
        
        return false;
    });
    
    // 打开程序表单
    function openProgramForm(data){
        var title = data ? '编辑程序' : '添加程序';
        
        layer.open({
            type: 1,
            title: title,
            area: ['500px', '400px'],
            content: $('#program-form').html(),
            success: function(layero, index){
                // 渲染表单
                form.render(null, 'program-form');
                
                // 如果是编辑，填充表单数据
                if(data){
                    form.val('program-form', {
                        'id': data.id,
                        'name': data.name,
                        'version': data.version,
                        'path': data.path,
                        'description': data.description
                    });
                }
            }
        });
    }
    
    // 打开上传表单
    function openUploadForm(){
        layer.open({
            type: 1,
            title: '上传程序包',
            area: ['600px', '400px'],
            content: $('#upload-form').html(),
            success: function(layero, index){
                // 初始化上传组件
                var uploadListView = $('#upload-list');
                var uploadListIns = upload.render({
                    elem: '#program-upload',
                    url: 'ajax.php?action=upload_program',
                    accept: 'file',
                    exts: 'zip',
                    data: {
                        csrf_token: '<?php echo $csrfToken; ?>'
                    },
                    multiple: false,
                    auto: false,
                    bindAction: '#upload-start',
                    choose: function(obj){
                        var files = this.files = obj.pushFile();
                        
                        // 预览选择的文件
                        obj.preview(function(index, file, result){
                            var tr = $(['<tr id="upload-'+ index +'">',
                                '<td>'+ file.name +'</td>',
                                '<td>'+ (file.size/1024).toFixed(2) +'KB</td>',
                                '<td>等待上传</td>',
                                '<td>',
                                    '<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>',
                                    '<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>',
                                '</td>',
                            '</tr>'].join(''));
                            
                            // 删除
                            tr.find('.demo-delete').on('click', function(){
                                delete files[index];
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = '';
                            });
                            
                            // 重传
                            tr.find('.demo-reload').on('click', function(){
                                obj.upload(index, file);
                            });
                            
                            uploadListView.append(tr);
                        });
                    },
                    done: function(res, index, upload){
                        if(res.code === 0){
                            var tr = uploadListView.find('tr#upload-'+ index);
                            var tds = tr.children();
                            tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                            tds.eq(3).html('');
                            
                            // 刷新表格
                            programTable.reload();
                            
                            // 关闭窗口
                            setTimeout(function(){
                                layer.close(layer.index);
                                layer.msg(res.msg, {icon: 1});
                            }, 1000);
                            
                            return delete this.files[index];
                        }
                        
                        this.error(index, upload);
                    },
                    error: function(index, upload){
                        var tr = uploadListView.find('tr#upload-'+ index);
                        var tds = tr.children();
                        tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                        tds.eq(3).find('.demo-reload').removeClass('layui-hide');
                    }
                });
            }
        });
    }
});
</script>

<?php
// 包含底部
require_once 'common/footer.php';
?>