<?php
/**
 * 操作日志页面
 */
// 包含头部
require_once 'common/header.php';

// 生成CSRF令牌
$csrfToken = generateCsrfToken();
?>

<div class="layui-card">
    <div class="layui-card-header">
        <span>操作日志</span>
        <button class="layui-btn layui-btn-xs layui-btn-danger" id="clear-logs-btn" style="float:right;">清空日志</button>
    </div>
    <div class="layui-card-body">
        <!-- 搜索表单 -->
        <form class="layui-form" id="search-form" style="margin-bottom: 10px;">
            <div class="layui-inline">
                <div class="layui-input-inline" style="width: 150px;">
                    <input type="text" name="admin_username" placeholder="管理员用户名" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-input-inline" style="width: 120px;">
                    <select name="action">
                        <option value="">操作类型</option>
                        <option value="add_domain">添加域名</option>
                        <option value="update_domain">更新域名</option>
                        <option value="delete_domain">删除域名</option>
                        <option value="add_program">添加程序</option>
                        <option value="update_program">更新程序</option>
                        <option value="delete_program">删除程序</option>
                        <option value="update_settings">更新设置</option>
                        <option value="update_password">修改密码</option>
                        <option value="backup_db">备份数据库</option>
                        <option value="clear_temp">清理临时文件</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-input-inline" style="width: 120px;">
                    <input type="text" name="start_date" id="start-date" placeholder="开始日期" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-input-inline" style="width: 120px;">
                    <input type="text" name="end_date" id="end-date" placeholder="结束日期" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn" lay-submit lay-filter="search-submit">搜索</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </form>
        
        <!-- 数据表格 -->
        <table id="log-table" lay-filter="log-table"></table>
    </div>
</div>

<script>
layui.use(['admin', 'form', 'table', 'laydate', 'layer'], function(){
    var admin = layui.admin;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;
    var layer = layui.layer;
    var $ = layui.jquery;
    
    // 初始化日期选择器
    admin.initDate('#start-date');
    admin.initDate('#end-date');
    
    // 渲染表格
    var logTable = admin.renderTable({
        elem: '#log-table',
        url: 'ajax.php?action=get_logs',
        cols: [[
            {field: 'id', title: 'ID', width: 80, sort: true},
            {field: 'admin_username', title: '管理员', width: 100},
            {field: 'action', title: '操作类型', width: 120},
            {field: 'description', title: '操作描述'},
            {field: 'ip', title: 'IP地址', width: 120},
            {field: 'create_time', title: '操作时间', width: 170, sort: true}
        ]]
    });
    
    // 处理搜索表单提交
    form.on('submit(search-submit)', function(data){
        var field = data.field;
        
        // 重载表格
        logTable.reload({
            where: field,
            page: {
                curr: 1
            }
        });
        
        return false;
    });
    
    // 清空日志按钮点击事件
    $('#clear-logs-btn').on('click', function(){
        layer.confirm('确定要清空所有操作日志吗？此操作不可恢复！', {
            btn: ['确定清空','取消']
        }, function(){
            // 发送清空请求
            $.ajax({
                url: 'ajax.php?action=clear_logs',
                type: 'POST',
                data: {
                    csrf_token: '<?php echo $csrfToken; ?>'
                },
                dataType: 'json',
                success: function(res){
                    if(res.code === 0){
                        layer.msg(res.msg, {icon: 1});
                        // 刷新表格
                        logTable.reload();
                    } else {
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });
        });
    });
});
</script>

<?php
// 包含底部
require_once 'common/footer.php';
?>