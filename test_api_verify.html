<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API验证测试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .preset-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .preset-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .preset-btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API验证测试工具</h1>
        <p>用于测试 verify_auth 接口的试用期判断功能</p>

        <div class="preset-buttons">
            <button class="preset-btn" onclick="setPreset('trial_valid')">试用期内</button>
            <button class="preset-btn" onclick="setPreset('trial_expired')">试用过期</button>
            <button class="preset-btn" onclick="setPreset('formal_valid')">正式授权</button>
            <button class="preset-btn" onclick="setPreset('formal_expired')">正式过期</button>
            <button class="preset-btn" onclick="setPreset('invalid_code')">无效授权码</button>
        </div>

        <form id="testForm">
            <div class="form-group">
                <label for="action">API动作</label>
                <select id="action" name="action">
                    <option value="verify_auth">verify_auth - 验证授权</option>
                    <option value="check_domain">check_domain - 检查域名</option>
                    <option value="activate_trial">activate_trial - 激活试用</option>
                </select>
            </div>

            <div class="form-group">
                <label for="domain">域名</label>
                <input type="text" id="domain" name="domain" value="trial-test.example.com" placeholder="输入测试域名">
            </div>

            <div class="form-group" id="authCodeGroup">
                <label for="authCode">授权码</label>
                <input type="text" id="authCode" name="auth_code" value="TEST_TRIAL_CODE_123" placeholder="输入授权码">
            </div>

            <div class="form-group">
                <label for="clientIp">客户端IP</label>
                <input type="text" id="clientIp" name="client_ip" value="127.0.0.1" placeholder="客户端IP地址">
            </div>

            <button type="submit">🚀 发送请求</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        // 预设配置
        const presets = {
            trial_valid: {
                domain: 'trial-test.example.com',
                auth_code: 'TEST_TRIAL_CODE_123',
                description: '有效的试用授权'
            },
            trial_expired: {
                domain: 'trial-expired.example.com',
                auth_code: 'EXPIRED_TRIAL_CODE',
                description: '过期的试用授权'
            },
            formal_valid: {
                domain: 'formal-test.example.com',
                auth_code: 'FORMAL_AUTH_CODE_456',
                description: '有效的正式授权'
            },
            formal_expired: {
                domain: 'formal-expired.example.com',
                auth_code: 'EXPIRED_FORMAL_CODE',
                description: '过期的正式授权'
            },
            invalid_code: {
                domain: 'invalid-test.example.com',
                auth_code: 'INVALID_CODE_999',
                description: '无效的授权码'
            }
        };

        function setPreset(type) {
            const preset = presets[type];
            if (preset) {
                document.getElementById('domain').value = preset.domain;
                document.getElementById('authCode').value = preset.auth_code;
                showResult(`已设置预设: ${preset.description}`, 'info');
            }
        }

        function generateToken(authCode, domain) {
            const today = new Date();
            const dateStr = today.getFullYear() + 
                           String(today.getMonth() + 1).padStart(2, '0') + 
                           String(today.getDate()).padStart(2, '0');
            console.log(authCode + dateStr + domain);
            return md5(authCode + dateStr + domain);
        }

        function showResult(content, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = content;
        }

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const action = formData.get('action');
            const domain = formData.get('domain');
            const authCode = formData.get('auth_code');
            
            // 为 verify_auth 添加必要的参数
            if (action === 'verify_auth') {
                const timestamp = Math.floor(Date.now() / 1000);
                formData.append('timestamp', timestamp);
                formData.append('verify_token', '00a682129fcb38eaf0837c51a18a7f05');
            }

            try {
                showResult('正在发送请求...', 'info');
                
                const response = await fetch('/api/ajax.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.text();
                
                try {
                    const jsonResult = JSON.parse(result);
                    const formatted = JSON.stringify(jsonResult, null, 2);
                    
                    if (jsonResult.code === 0) {
                        showResult(`✅ 请求成功\n\n${formatted}`, 'success');
                    } else {
                        showResult(`❌ 请求失败\n\n${formatted}`, 'error');
                    }
                } catch (e) {
                    showResult(`📄 原始响应\n\n${result}`, 'info');
                }
                
            } catch (error) {
                showResult(`🚫 网络错误\n\n${error.message}`, 'error');
            }
        });

        // 根据选择的动作显示/隐藏授权码字段
        document.getElementById('action').addEventListener('change', function() {
            const authCodeGroup = document.getElementById('authCodeGroup');
            if (this.value === 'verify_auth') {
                authCodeGroup.style.display = 'block';
            } else {
                authCodeGroup.style.display = 'none';
            }
        });

        // 简单的MD5实现（仅用于测试）
        function md5(string) {
            // 这里应该使用真正的MD5库，这只是一个简化版本
            // 在实际使用中，请使用 crypto-js 或其他MD5库
            return btoa(string).replace(/[^a-zA-Z0-9]/g, '').toLowerCase().substring(0, 32);
        }
    </script>
</body>
</html>
