<?php
/**
 * 通用函数库
 */

/**
 * 格式化响应数据
 * 
 * @param int $code 状态码 0成功 非0失败
 * @param string $msg 消息
 * @param array $data 数据
 * @return array 格式化后的响应数组
 */
function formatResponse($code, $msg = '', $data = []) {
    return [
        'code' => $code,
        'msg' => $msg,
        'data' => $data
    ];
}

/**
 * 输出JSON响应
 * 
 * @param int $code 状态码 0成功 非0失败
 * @param string $msg 消息
 * @param array $data 数据
 */
function jsonResponse($code, $msg = '', $data = []) {
    $response = formatResponse($code, $msg, $data);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 检查是否为AJAX请求
 * 
 * @return bool 是否为AJAX请求
 */
function isAjax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}

/**
 * 获取客户端IP地址
 * 
 * @return string IP地址
 */
function getClientIp() {
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

/**
 * 检查域名格式是否合法
 * 
 * @param string $domain 域名
 * @return bool 是否合法
 */
function isValidDomain($domain) {
    return preg_match('/^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/', $domain);
}

/**
 * 清理输入数据
 * 
 * @param string $data 输入数据
 * @return string 清理后的数据
 */
function cleanInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * 检查用户是否登录
 * 
 * @return bool 是否已登录
 */
function isLoggedIn() {
    return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
}

/**
 * 重定向页面
 * 
 * @param string $url 目标URL
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * 生成CSRF令牌
 * 
 * @return string 令牌
 */
function generateCsrfToken() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * 验证CSRF令牌
 * 
 * @param string $token 待验证的令牌
 * @return bool 是否有效
 */
function validateCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * 记录日志
 * 
 * @param string $message 日志消息
 * @param string $level 日志级别 (info, warning, error)
 */
function logMessage($message, $level = 'info') {
    $logFile = ROOT_PATH . '/logs/' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $time = date('Y-m-d H:i:s');
    $ip = getClientIp();
    $logMessage = "[$time][$level][$ip] $message" . PHP_EOL;
    
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

/**
 * 获取当前页面URL
 * 
 * @return string 当前页面URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    return $protocol . '://' . $host . $uri;
}

/**
 * 格式化文件大小
 * 
 * @param int $size 文件大小（字节）
 * @return string 格式化后的大小
 */
function formatFileSize($size) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($size >= 1024 && $i < count($units) - 1) {
        $size /= 1024;
        $i++;
    }
    return round($size, 2) . ' ' . $units[$i];
}

/**
 * 生成随机字符串
 * 
 * @param int $length 长度
 * @return string 随机字符串
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charLength - 1)];
    }
    return $randomString;
}

/**
 * 加载视图模板
 * 
 * @param string $template 模板名称
 * @param array $data 传递给模板的数据
 */
function loadTemplate($template, $data = []) {
    // 将数据数组变量展开到当前作用域
    extract($data);
    
    // 加载模板文件
    $templateFile = TEMPLATE_PATH . $template . '.php';
    if (file_exists($templateFile)) {
        require_once $templateFile;
    } else {
        die('模板文件不存在: ' . $template);
    }
}

/**
 * 计算剩余天数
 * 
 * @param string $endDate 结束日期，格式：Y-m-d H:i:s
 * @return int 剩余天数
 */
function calculateRemainingDays($endDate) {
    $endTime = strtotime($endDate);
    $now = time();
    $diff = $endTime - $now;
    
    if ($diff <= 0) {
        return 0;
    }
    
    return ceil($diff / 86400); // 86400秒 = 1天
}

/**
 * 检查并创建目录
 * 
 * @param string $dir 目录路径
 * @return bool 成功返回true，失败返回false
 */
function checkAndCreateDir($dir) {
    if (!is_dir($dir)) {
        return mkdir($dir, 0755, true);
    }
    return true;
}

/**
 * 获取文件扩展名
 * 
 * @param string $filename 文件名
 * @return string 扩展名
 */
function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * 检查文件是否为图片
 * 
 * @param string $file 文件路径
 * @return bool 是否为图片
 */
function isImage($file) {
    $ext = getFileExtension($file);
    $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return in_array($ext, $imageTypes);
}

/**
 * 显示分页
 * 
 * @param int $total 总记录数
 * @param int $page 当前页码
 * @param int $limit 每页记录数
 * @param string $url 分页URL模板，使用{page}作为页码占位符
 * @return string 分页HTML
 */
function showPagination($total, $page, $limit, $url = '?page={page}') {
    $totalPages = ceil($total / $limit);
    
    if ($totalPages <= 1) {
        return '';
    }
    
    $html = '<div class="layui-box layui-laypage layui-laypage-default">';
    
    // 上一页
    if ($page > 1) {
        $prevUrl = str_replace('{page}', $page - 1, $url);
        $html .= '<a href="' . $prevUrl . '" class="layui-laypage-prev">上一页</a>';
    } else {
        $html .= '<a href="javascript:;" class="layui-laypage-prev layui-disabled">上一页</a>';
    }
    
    // 页码
    $start = max(1, $page - 2);
    $end = min($totalPages, $page + 2);
    
    if ($start > 1) {
        $html .= '<a href="' . str_replace('{page}', 1, $url) . '">1</a>';
        if ($start > 2) {
            $html .= '<span class="layui-laypage-spr">…</span>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $page) {
            $html .= '<span class="layui-laypage-curr"><em class="layui-laypage-em"></em><em>' . $i . '</em></span>';
        } else {
            $pageUrl = str_replace('{page}', $i, $url);
            $html .= '<a href="' . $pageUrl . '">' . $i . '</a>';
        }
    }
    
    if ($end < $totalPages) {
        if ($end < $totalPages - 1) {
            $html .= '<span class="layui-laypage-spr">…</span>';
        }
        $html .= '<a href="' . str_replace('{page}', $totalPages, $url) . '">' . $totalPages . '</a>';
    }
    
    // 下一页
    if ($page < $totalPages) {
        $nextUrl = str_replace('{page}', $page + 1, $url);
        $html .= '<a href="' . $nextUrl . '" class="layui-laypage-next">下一页</a>';
    } else {
        $html .= '<a href="javascript:;" class="layui-laypage-next layui-disabled">下一页</a>';
    }
    
    $html .= '</div>';
    
    return $html;
}