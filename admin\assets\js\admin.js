/**
 * 管理后台通用JS函数库
 */
layui.define(['layer', 'form', 'table', 'laydate', 'upload'], function(exports) {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;
    var upload = layui.upload;
    
    var admin = {
        /**
         * 通用表单提交处理
         * @param {Object} options 配置选项
         */
        submitForm: function(options) {
            var defaults = {
                formFilter: '',           // 表单过滤器
                submitFilter: '',         // 提交按钮过滤器
                url: '',                  // 提交URL
                successCallback: null,    // 成功回调
                errorCallback: null       // 错误回调
            };
            
            var opts = $.extend(defaults, options);
            
            form.on('submit(' + opts.submitFilter + ')', function(data) {
                var field = data.field;
                
                // 发送请求
                $.ajax({
                    url: opts.url,
                    type: 'POST',
                    data: field,
                    dataType: 'json',
                    success: function(res) {
                        if(res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            if(opts.successCallback) {
                                opts.successCallback(res);
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2});
                            if(opts.errorCallback) {
                                opts.errorCallback(res);
                            }
                        }
                    }
                });
                
                return false;
            });
        },
        
        /**
         * 通用表格配置
         * @param {Object} options 配置选项
         * @returns {Object} 表格实例
         */
        renderTable: function(options) {
            var defaults = {
                elem: '',                 // 表格元素ID
                url: '',                  // 数据接口
                cols: [],                 // 表头配置
                toolbar: '',              // 工具栏
                defaultToolbar: ['filter', 'exports', 'print'],  // 默认工具栏
                page: true,               // 开启分页
                limit: 10,                // 每页数量
                limits: [10, 20, 50, 100],// 每页数量选择列表
                height: 'full-220',       // 高度
                text: {                   // 自定义文本
                    none: '暂无数据'
                },
                done: null                // 渲染完成回调
            };
            
            var opts = $.extend(defaults, options);
            
            // 添加响应处理
            if (!opts.parseData) {
                opts.parseData = function(res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count,
                        "data": res.data.list
                    };
                };
            }
            
            if (!opts.response) {
                opts.response = {
                    statusCode: 0
                };
            }
            
            return table.render(opts);
        },
        
        /**
         * 批量删除处理
         * @param {Object} options 配置选项
         */
        batchDelete: function(options) {
            var defaults = {
                tableId: '',              // 表格ID
                url: '',                  // 删除接口
                csrfToken: '',            // CSRF令牌
                confirmMsg: '确定删除选中的记录吗？',  // 确认提示
                successCallback: null     // 成功回调
            };
            
            var opts = $.extend(defaults, options);
            
            var checkStatus = table.checkStatus(opts.tableId);
            var data = checkStatus.data;
            
            if(data.length === 0) {
                layer.msg('请选择要删除的记录');
                return;
            }
            
            layer.confirm(opts.confirmMsg, function(index) {
                var ids = [];
                for(var i = 0; i < data.length; i++) {
                    ids.push(data[i].id);
                }
                
                // 发送删除请求
                $.ajax({
                    url: opts.url,
                    type: 'POST',
                    data: {
                        ids: ids,
                        csrf_token: opts.csrfToken
                    },
                    dataType: 'json',
                    success: function(res) {
                        if(res.code === 0) {
                            layer.msg(res.msg, {icon: 1});
                            if(opts.successCallback) {
                                opts.successCallback(res);
                            }
                        } else {
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
                
                layer.close(index);
            });
        },
        
        /**
         * 初始化日期选择器
         * @param {String|Object} elem 元素选择器或元素
         * @param {Object} options 配置选项
         */
        initDate: function(elem, options) {
            var defaults = {
                type: 'date',
                trigger: 'click',
                format: 'yyyy-MM-dd',
                min: '1900-1-1',
                max: '2099-12-31'
            };
            
            var opts = $.extend(defaults, options);
            laydate.render($.extend({elem: elem}, opts));
        },
        
        /**
         * 打开表单弹窗
         * @param {Object} options 配置选项
         */
        openForm: function(options) {
            var defaults = {
                title: '表单',
                content: '',
                area: ['500px', '400px'],
                success: null
            };
            
            var opts = $.extend(defaults, options);
            
            return layer.open({
                type: 1,
                title: opts.title,
                area: opts.area,
                content: opts.content,
                success: opts.success
            });
        },
        
        /**
         * 通用文件上传
         * @param {Object} options 配置选项
         */
        upload: function(options) {
            var defaults = {
                elem: '',                 // 绑定元素
                url: '',                  // 上传接口
                data: {},                 // 额外数据
                accept: 'file',           // 接受类型
                exts: '',                 // 允许后缀
                size: 10240,              // 最大大小KB
                multiple: false,          // 是否多文件
                before: null,             // 上传前回调
                done: null,               // 上传完成回调
                error: null               // 上传错误回调
            };
            
            var opts = $.extend(defaults, options);
            
            return upload.render(opts);
        }
    };
    
    exports('admin', admin);
});