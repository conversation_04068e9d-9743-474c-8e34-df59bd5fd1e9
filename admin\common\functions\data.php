<?php
/**
 * 管理后台数据处理通用函数
 */

/**
 * 获取数据列表（通用分页查询）
 * 
 * @param string $table 表名
 * @param string $whereCondition WHERE条件语句
 * @param array $params 查询参数
 * @param string $orderBy 排序规则
 * @return array 包含总数和列表数据的数组
 */
function getDataList($table, $whereCondition = '', $params = [], $orderBy = 'id DESC') {
    $db = DB::getInstance();
    
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where = $whereCondition;
    
    // 获取总记录数
    $countSql = "SELECT COUNT(*) as total FROM {$db->getPrefix()}{$table}" . (empty($where) ? "" : " WHERE {$where}");
    $totalResult = $db->query($countSql, $params)->fetch();
    $total = $totalResult['total'];
    
    // 获取分页数据
    $dataSql = "SELECT * FROM {$db->getPrefix()}{$table}" . (empty($where) ? "" : " WHERE {$where}") . " ORDER BY {$orderBy} LIMIT {$offset}, {$limit}";
    $data = $db->query($dataSql, $params)->fetchAll();
    
    return [
        'count' => $total,
        'list' => $data
    ];
}

/**
 * 删除单条记录
 * 
 * @param string $table 表名
 * @param int $id 记录ID
 * @return bool 删除结果
 */
function deleteRecord($table, $id) {
    $db = DB::getInstance();
    return $db->delete($table, ['id' => intval($id)]);
}

/**
 * 批量删除记录
 * 
 * @param string $table 表名
 * @param array $ids ID数组
 * @return int 成功删除的记录数
 */
function batchDeleteRecords($table, $ids) {
    $db = DB::getInstance();
    $success = 0;
    
    foreach ($ids as $id) {
        $result = $db->delete($table, ['id' => intval($id)]);
        if ($result) {
            $success++;
        }
    }
    
    return $success;
}

/**
 * 验证表单CSRF令牌
 * 
 * @param string $token 提交的令牌
 * @return bool 验证结果
 */
function validateAdminCsrfToken($token) {
    if (empty($token) || !isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        return false;
    }
    return true;
}